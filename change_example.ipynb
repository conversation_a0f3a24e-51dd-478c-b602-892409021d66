import json

import numpy as np

with open('raw_data/ts.json', 'r') as f:
    data = json.load(f) 

data['info'] = 'change'

data['id'] = 123

data['dict'] = {}

with open('raw_data/ts_more.json', 'w') as f:
    json.dump(data, f)

data['ts']['value'][20:40] = [np.nan] * 20



data['ts']['value'][150:200] = [np.nan] * 50

data['ts']['value'][400:500] = [np.nan] * 100

data['ts']['value'][700:800] = [np.nan] * 100

data['ts']['value'][1000:1100] = [np.nan] * 100

data['ts']['value'][1300:1400] = [np.nan] * 100

with open('raw_data/ts_nan.json', 'w') as f:
    json.dump(data, f)

with open('raw_data/ts_nan.json', 'r') as f:
    data = json.load(f) 

data['ts']['value']