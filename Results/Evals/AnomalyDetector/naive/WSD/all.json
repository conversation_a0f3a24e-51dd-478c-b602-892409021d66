{"121": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6.8125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6.8125}}, "135": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 11.8125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 11.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 11.8125}}, "109": {"best f1 under pa": {"f1": 0.9268292682926823, "precision": 0.8636363636363636, "recall": 1.0, "threshold": 403.0}, "event-based f1 under pa with mode log": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.875, "threshold": 1008.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.8749999999999998, "threshold": 1008.0}}, "34": {"best f1 under pa": {"f1": 0.9976580796252922, "precision": 0.9953271028037384, "recall": 1.0, "threshold": 10.260000000000002}, "event-based f1 under pa with mode log": {"f1": 0.9523809523809517, "precision": 0.909090909090909, "recall": 0.9999999999999998, "threshold": 10.260000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 10.260000000000002}}, "35": {"best f1 under pa": {"f1": 0.996913580246913, "precision": 0.9938461538461538, "recall": 1.0, "threshold": 6.649999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9642857142857137, "precision": 0.9310344827586207, "recall": 1.0, "threshold": 6.649999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 6.649999999999999}}, "21": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6067.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 6067.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6067.0}}, "108": {"best f1 under pa": {"f1": 0.9821428571428567, "precision": 0.9649122807017544, "recall": 1.0, "threshold": 421.5}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 421.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 1762.0}}, "134": {"best f1 under pa": {"f1": 0.9811320754716976, "precision": 0.9629629629629629, "recall": 1.0, "threshold": 7.0}, "event-based f1 under pa with mode log": {"f1": 0.8461538461538455, "precision": 0.846153846153846, "recall": 0.846153846153846, "threshold": 8.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 12.8416671753}}, "120": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4.0}}, "136": {"best f1 under pa": {"f1": 0.9279999999999994, "precision": 0.9508196721311475, "recall": 0.90625, "threshold": 7.5}, "event-based f1 under pa with mode log": {"f1": 0.7826086956521733, "precision": 0.7499999999999999, "recall": 0.818181818181818, "threshold": 7.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.7499999999999999, "recall": 0.5999999999999999, "threshold": 8.375}}, "122": {"best f1 under pa": {"f1": 0.9872611464968147, "precision": 1.0, "recall": 0.9748427672955975, "threshold": 4.4375}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.9999999999999999, "recall": 0.857142857142857, "threshold": 4.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.9999999999999996, "recall": 0.6666666666666665, "threshold": 4.4375}}, "37": {"best f1 under pa": {"f1": 0.9812332439678279, "precision": 1.0, "recall": 0.9631578947368421, "threshold": 10.049999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9499999999999995, "precision": 1.0, "recall": 0.9047619047619048, "threshold": 10.049999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.9999999999999998, "recall": 0.8888888888888887, "threshold": 10.049999999999997}}, "23": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6794.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6794.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6794.0}}, "22": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6492.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 6492.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6492.0}}, "36": {"best f1 under pa": {"f1": 0.9927007299270069, "precision": 0.9855072463768116, "recall": 1.0, "threshold": 8.440000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9523809523809518, "precision": 0.9090909090909091, "recall": 1.0, "threshold": 8.440000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 8.440000000000005}}, "123": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.6875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5.6875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5.6875}}, "137": {"best f1 under pa": {"f1": 0.954003407155025, "precision": 0.9120521172638436, "recall": 1.0, "threshold": 7.600000381500003}, "event-based f1 under pa with mode log": {"f1": 0.705882352941176, "precision": 0.72, "recall": 0.6923076923076923, "threshold": 10.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.6315789473684205, "precision": 0.857142857142857, "recall": 0.49999999999999994, "threshold": 14.5625}}, "133": {"best f1 under pa": {"f1": 0.9937888198757758, "precision": 0.9876543209876543, "recall": 1.0, "threshold": 8.4375}, "event-based f1 under pa with mode log": {"f1": 0.950819672131147, "precision": 0.90625, "recall": 1.0, "threshold": 8.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.8965517241379304, "precision": 0.8125, "recall": 0.9999999999999999, "threshold": 8.4375}}, "32": {"best f1 under pa": {"f1": 0.994366197183098, "precision": 0.988795518207283, "recall": 1.0, "threshold": 1.3333334922800004}, "event-based f1 under pa with mode log": {"f1": 0.9574468085106378, "precision": 0.9183673469387755, "recall": 1.0, "threshold": 1.3333334922800004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9130434782608691, "precision": 0.84, "recall": 1.0, "threshold": 1.3333334922800004}}, "26": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6255.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 6255.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6255.0}}, "27": {"best f1 under pa": {"f1": 0.9695431472081212, "precision": 0.9408866995073891, "recall": 1.0, "threshold": 1.5}, "event-based f1 under pa with mode log": {"f1": 0.8955223880597009, "precision": 0.9090909090909091, "recall": 0.8823529411764706, "threshold": 2.7500002384200006}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428565, "precision": 0.8333333333333334, "recall": 0.8823529411764706, "threshold": 2.7500002384200006}}, "33": {"best f1 under pa": {"f1": 0.9872122762148332, "precision": 0.9747474747474747, "recall": 1.0, "threshold": 4.549999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.9999999999999999, "recall": 0.8235294117647058, "threshold": 18.79}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.9999999999999998, "recall": 0.8333333333333333, "threshold": 18.79}}, "132": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 14.6875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 14.6875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 14.6875}}, "118": {"best f1 under pa": {"f1": 0.8807339449541278, "precision": 0.9696969696969697, "recall": 0.8067226890756303, "threshold": 1443.0}, "event-based f1 under pa with mode log": {"f1": 0.7368421052631573, "precision": 0.6999999999999998, "recall": 0.7777777777777777, "threshold": 1443.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.9999999999999996, "recall": 0.4999999999999999, "threshold": 6281.0}}, "124": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.1875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4.1875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4.1875}}, "25": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4641.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4641.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4641.0}}, "31": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4166667461400002}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4166667461400002}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4166667461400002}}, "19": {"best f1 under pa": {"f1": 0.9868421052631573, "precision": 0.974025974025974, "recall": 1.0, "threshold": 902.0}, "event-based f1 under pa with mode log": {"f1": 0.8333333333333327, "precision": 0.7142857142857142, "recall": 0.9999999999999998, "threshold": 902.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.4999999999999999, "recall": 0.9999999999999996, "threshold": 902.0}}, "30": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5000002384200006}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5000002384200006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5000002384200006}}, "131": {"best f1 under pa": {"f1": 0.9538461538461533, "precision": 0.9117647058823529, "recall": 1.0, "threshold": 2.1875}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999994, "precision": 0.6, "recall": 0.9999999999999998, "threshold": 2.1875}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.9999999999999996, "recall": 0.4999999999999999, "threshold": 21.375}}, "125": {"best f1 under pa": {"f1": 0.9111969111969106, "precision": 0.8368794326241135, "recall": 1.0, "threshold": 2.125}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428564, "precision": 0.857142857142857, "recall": 0.857142857142857, "threshold": 4.75}, "event-based f1 under pa with mode squeeze": {"f1": 0.7692307692307686, "precision": 0.7142857142857142, "recall": 0.8333333333333333, "threshold": 4.75}}, "119": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5636.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5636.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5636.0}}, "142": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 3.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3.0}}, "156": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 404.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 404.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 404.0}}, "80": {"best f1 under pa": {"f1": 0.9952963311382872, "precision": 0.9906367041198502, "recall": 1.0, "threshold": 19.48}, "event-based f1 under pa with mode log": {"f1": 0.8695652173913038, "precision": 0.9090909090909091, "recall": 0.8333333333333334, "threshold": 47.709999999999994}, "event-based f1 under pa with mode squeeze": {"f1": 0.7142857142857136, "precision": 0.7142857142857142, "recall": 0.7142857142857142, "threshold": 47.709999999999994}}, "94": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 733.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 733.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 733.0}}, "181": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 517.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 517.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 517.0}}, "5": {"best f1 under pa": {"f1": 0.9025270758122739, "precision": 0.9541984732824428, "recall": 0.8561643835616438, "threshold": 10.666666984499999}, "event-based f1 under pa with mode log": {"f1": 0.7857142857142851, "precision": 0.846153846153846, "recall": 0.7333333333333333, "threshold": 15.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 15.0}}, "57": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.2526314258600006}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.2526314258600006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.2526314258600006}}, "4": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 18.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 18.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 18.25}}, "194": {"best f1 under pa": {"f1": 0.9945553539019959, "precision": 0.9891696750902527, "recall": 1.0, "threshold": 2001.0}, "event-based f1 under pa with mode log": {"f1": 0.8235294117647052, "precision": 0.6999999999999998, "recall": 0.9999999999999999, "threshold": 2001.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 4324.0}}, "180": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 718.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 718.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 718.0}}, "95": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 690.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 690.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 690.0}}, "157": {"best f1 under pa": {"f1": 0.9963369963369957, "precision": 0.9927007299270073, "recall": 1.0, "threshold": 497.0}, "event-based f1 under pa with mode log": {"f1": 0.9756097560975604, "precision": 0.9523809523809523, "recall": 1.0, "threshold": 497.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9523809523809517, "precision": 0.909090909090909, "recall": 0.9999999999999998, "threshold": 497.0}}, "143": {"best f1 under pa": {"f1": 0.9319371727748685, "precision": 0.8725490196078431, "recall": 1.0, "threshold": 1.125}, "event-based f1 under pa with mode log": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 2.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999994, "precision": 0.49999999999999994, "recall": 0.7499999999999999, "threshold": 2.5}}, "209": {"best f1 under pa": {"f1": 0.9683098591549291, "precision": 0.9385665529010239, "recall": 1.0, "threshold": 5.190000000000001}, "event-based f1 under pa with mode log": {"f1": 0.7586206896551718, "precision": 0.846153846153846, "recall": 0.6875, "threshold": 15.699999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 21.5}}, "155": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 539.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 539.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 539.0}}, "97": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4733.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 4733.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4733.0}}, "169": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 826.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 826.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 826.0}}, "83": {"best f1 under pa": {"f1": 0.9992051776995567, "precision": 0.9984116178806445, "recall": 1.0, "threshold": 1050.0}, "event-based f1 under pa with mode log": {"f1": 0.6666666666666661, "precision": 0.49999999999999994, "recall": 0.9999999999999999, "threshold": 1050.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.22222222222222193, "precision": 0.12499999999999997, "recall": 0.9999999999999989, "threshold": 1050.0}}, "68": {"best f1 under pa": {"f1": 0.9859154929577458, "precision": 0.9722222222222222, "recall": 1.0, "threshold": 1611.0}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 1611.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 1611.0}}, "196": {"best f1 under pa": {"f1": 0.9620253164556958, "precision": 0.926829268292683, "recall": 1.0, "threshold": 4085.0}, "event-based f1 under pa with mode log": {"f1": 0.7272727272727267, "precision": 0.5714285714285714, "recall": 0.9999999999999998, "threshold": 4085.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 5765.0}}, "182": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1699.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1699.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1699.0}}, "54": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 15.40000081058}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 15.40000081058}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 15.40000081058}}, "183": {"best f1 under pa": {"f1": 0.9819819819819814, "precision": 0.9646017699115044, "recall": 1.0, "threshold": 1114.0}, "event-based f1 under pa with mode log": {"f1": 0.8749999999999994, "precision": 0.7777777777777778, "recall": 0.9999999999999999, "threshold": 1114.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.777777777777777, "precision": 0.6363636363636362, "recall": 0.9999999999999999, "threshold": 1114.0}}, "69": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 17.21}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 17.21}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 17.21}}, "197": {"best f1 under pa": {"f1": 0.49681528662420343, "precision": 0.3305084745762712, "recall": 1.0, "threshold": 3091.5}, "event-based f1 under pa with mode log": {"f1": 0.28571428571428514, "precision": 0.24999999999999994, "recall": 0.33333333333333326, "threshold": 4565.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.19999999999999954, "precision": 0.14285714285714285, "recall": 0.33333333333333326, "threshold": 4565.0}}, "96": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4522.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4522.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4522.0}}, "168": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 590.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 590.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 590.0}}, "140": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.941666662693}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.941666662693}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 2.941666662693}}, "154": {"best f1 under pa": {"f1": 0.9962264150943391, "precision": 0.9924812030075187, "recall": 1.0, "threshold": 350.0}, "event-based f1 under pa with mode log": {"f1": 0.9705882352941171, "precision": 0.9428571428571428, "recall": 1.0, "threshold": 350.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9677419354838703, "precision": 0.9999999999999999, "recall": 0.9375, "threshold": 428.0}}, "86": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4916.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4916.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 4916.0}}, "178": {"best f1 under pa": {"f1": 0.8275862068965512, "precision": 0.7058823529411765, "recall": 1.0, "threshold": 30.28}, "event-based f1 under pa with mode log": {"f1": 0.2857142857142854, "precision": 0.16666666666666666, "recall": 0.9999999999999997, "threshold": 30.28}, "event-based f1 under pa with mode squeeze": {"f1": 0.11764705882352929, "precision": 0.0625, "recall": 0.9999999999999989, "threshold": 30.28}}, "150": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1096.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1096.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1096.0}}, "144": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.9375}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.9375}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.9375}}, "45": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.64736819268}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.64736819268}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.64736819268}}, "193": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6027.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6027.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6027.0}}, "79": {"best f1 under pa": {"f1": 0.9975903614457826, "precision": 0.9951923076923077, "recall": 1.0, "threshold": 8.600000000000001}, "event-based f1 under pa with mode log": {"f1": 0.9756097560975604, "precision": 0.9523809523809523, "recall": 1.0, "threshold": 8.600000000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.8888888888888887, "recall": 0.9999999999999998, "threshold": 8.600000000000001}}, "187": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4187.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4187.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4187.0}}, "78": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 37.22}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 37.22}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 37.22}}, "186": {"best f1 under pa": {"f1": 0.9942196531791903, "precision": 0.9885057471264368, "recall": 1.0, "threshold": 3419.0}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888884, "precision": 0.8, "recall": 1.0, "threshold": 3419.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999993, "precision": 0.5999999999999999, "recall": 0.9999999999999999, "threshold": 3419.0}}, "192": {"best f1 under pa": {"f1": 0.9696969696969692, "precision": 0.9411764705882353, "recall": 1.0, "threshold": 4180.0}, "event-based f1 under pa with mode log": {"f1": 0.8181818181818175, "precision": 0.6923076923076922, "recall": 0.9999999999999998, "threshold": 4180.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.5999999999999999, "recall": 0.7499999999999999, "threshold": 5413.0}}, "50": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.2000002861000003}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1.2000002861000003}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 1.2000002861000003}}, "145": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0}}, "151": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 974.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 974.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 974.0}}, "179": {"best f1 under pa": {"f1": 0.9411764705882347, "precision": 0.8888888888888888, "recall": 1.0, "threshold": 32.629999999999995}, "event-based f1 under pa with mode log": {"f1": 0.6666666666666661, "precision": 0.4999999999999999, "recall": 0.9999999999999998, "threshold": 32.629999999999995}, "event-based f1 under pa with mode squeeze": {"f1": 0.4444444444444441, "precision": 0.2857142857142857, "recall": 0.9999999999999996, "threshold": 32.629999999999995}}, "93": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 817.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 817.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 817.0}}, "85": {"best f1 under pa": {"f1": 0.9466666666666662, "precision": 0.8987341772151899, "recall": 1.0, "threshold": 1368.0}, "event-based f1 under pa with mode log": {"f1": 0.7999999999999993, "precision": 0.9999999999999999, "recall": 0.6666666666666665, "threshold": 4399.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 4399.0}}, "147": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.375}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.375}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.375}}, "153": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1148.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1148.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1148.0}}, "46": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.4078946113600006}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.4078946113600006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2.4078946113600006}}, "184": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 451.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 451.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 451.0}}, "190": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4169.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4169.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 4169.0}}, "191": {"best f1 under pa": {"f1": 0.6306306306306301, "precision": 0.5737704918032787, "recall": 0.7, "threshold": 3775.0}, "event-based f1 under pa with mode log": {"f1": 0.21052631578947334, "precision": 0.13333333333333333, "recall": 0.4999999999999999, "threshold": 3775.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.12499999999999978, "precision": 0.07142857142857142, "recall": 0.4999999999999999, "threshold": 3775.0}}, "185": {"best f1 under pa": {"f1": 0.9503239740820729, "precision": 0.9777777777777777, "recall": 0.9243697478991597, "threshold": 298.0}, "event-based f1 under pa with mode log": {"f1": 0.928571428571428, "precision": 1.0, "recall": 0.8666666666666667, "threshold": 413.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9285714285714279, "precision": 0.9999999999999999, "recall": 0.8666666666666666, "threshold": 413.5}}, "53": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.0894739627800005}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.0894739627800005}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.0894739627800005}}, "47": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.794736862189999}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.794736862189999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2.794736862189999}}, "152": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 490.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 490.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 490.0}}, "146": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}}, "84": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4084.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4084.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4084.0}}, "201": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.46}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.46}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 9.46}}, "163": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 149.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 149.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 149.25}}, "177": {"best f1 under pa": {"f1": 0.9085173501577282, "precision": 0.8323699421965318, "recall": 1.0, "threshold": 22.44999999999999}, "event-based f1 under pa with mode log": {"f1": 0.6428571428571422, "precision": 0.5625, "recall": 0.7499999999999999, "threshold": 33.16999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999944, "precision": 0.3636363636363636, "recall": 0.7999999999999998, "threshold": 33.16999999999999}}, "89": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1476.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 1476.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 1476.0}}, "62": {"best f1 under pa": {"f1": 0.9969604863221879, "precision": 0.9939393939393939, "recall": 1.0, "threshold": 0.9499998092600004}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 0.9499998092600004}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 0.9499998092600004}}, "188": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2628.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2628.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2628.0}}, "176": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 192.88}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 192.88}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 192.88}}, "200": {"best f1 under pa": {"f1": 0.92063492063492, "precision": 0.8529411764705882, "recall": 1.0, "threshold": 18.509999999999998}, "event-based f1 under pa with mode log": {"f1": 0.545454545454545, "precision": 0.37499999999999994, "recall": 0.9999999999999997, "threshold": 18.509999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.2857142857142853, "precision": 0.16666666666666663, "recall": 0.9999999999999989, "threshold": 18.509999999999998}}, "202": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 7.760000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 7.760000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 7.760000000000005}}, "174": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 233.83}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 233.83}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 233.83}}, "160": {"best f1 under pa": {"f1": 0.9976247030878854, "precision": 0.995260663507109, "recall": 1.0, "threshold": 395.0}, "event-based f1 under pa with mode log": {"f1": 0.9787234042553186, "precision": 0.9583333333333334, "recall": 1.0, "threshold": 395.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.956521739130434, "precision": 0.9166666666666665, "recall": 0.9999999999999999, "threshold": 395.0}}, "148": {"best f1 under pa": {"f1": 0.9959349593495929, "precision": 0.9919028340080972, "recall": 1.0, "threshold": 1.875}, "event-based f1 under pa with mode log": {"f1": 0.9743589743589738, "precision": 0.95, "recall": 1.0, "threshold": 1.875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9473684210526311, "precision": 0.9, "recall": 1.0, "threshold": 1.875}}, "49": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5499997139000006}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 1.5499997139000006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 1.5499997139000006}}, "61": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.7499999999999998}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 0.7499999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 0.7499999999999998}}, "60": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 23.46842145923}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 23.46842145923}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 23.46842145923}}, "74": {"best f1 under pa": {"f1": 0.9835466179159044, "precision": 0.9676258992805755, "recall": 1.0, "threshold": 5.880000000000003}, "event-based f1 under pa with mode log": {"f1": 0.7906976744186042, "precision": 0.6538461538461539, "recall": 1.0, "threshold": 5.880000000000003}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.7999999999999998, "recall": 0.6666666666666665, "threshold": 14.469999999999999}}, "48": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 23.64999914173}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 23.64999914173}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 23.64999914173}}, "149": {"best f1 under pa": {"f1": 0.9993108201240518, "precision": 0.9986225895316805, "recall": 1.0, "threshold": 0.8125}, "event-based f1 under pa with mode log": {"f1": 0.9919999999999993, "precision": 0.9841269841269841, "recall": 1.0, "threshold": 0.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9824561403508766, "precision": 0.9655172413793104, "recall": 1.0, "threshold": 0.8125}}, "161": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 735.0}}, "203": {"best f1 under pa": {"f1": 0.9523809523809518, "precision": 0.9090909090909091, "recall": 1.0, "threshold": 4.859999999999999}, "event-based f1 under pa with mode log": {"f1": 0.8648648648648642, "precision": 1.0, "recall": 0.7619047619047619, "threshold": 9.259999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.8749999999999998, "threshold": 9.259999999999998}}, "207": {"best f1 under pa": {"f1": 0.9891304347826082, "precision": 0.978494623655914, "recall": 1.0, "threshold": 18.700000000000003}, "event-based f1 under pa with mode log": {"f1": 0.9411764705882347, "precision": 0.8888888888888888, "recall": 1.0, "threshold": 18.700000000000003}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999992, "precision": 0.7777777777777777, "recall": 0.9999999999999999, "threshold": 18.700000000000003}}, "159": {"best f1 under pa": {"f1": 0.9633802816901403, "precision": 0.9293478260869565, "recall": 1.0, "threshold": 435.0}, "event-based f1 under pa with mode log": {"f1": 0.9387755102040811, "precision": 0.9583333333333334, "recall": 0.92, "threshold": 601.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.916666666666666, "precision": 0.9166666666666665, "recall": 0.9166666666666665, "threshold": 601.0}}, "171": {"best f1 under pa": {"f1": 0.9931506849315063, "precision": 0.9863945578231292, "recall": 1.0, "threshold": 505.5}, "event-based f1 under pa with mode log": {"f1": 0.9523809523809518, "precision": 0.9090909090909091, "recall": 1.0, "threshold": 505.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 505.5}}, "165": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 146.76}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 146.76}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 146.76}}, "70": {"best f1 under pa": {"f1": 0.9974683544303792, "precision": 0.9949494949494949, "recall": 1.0, "threshold": 12.610000000000007}, "event-based f1 under pa with mode log": {"f1": 0.9599999999999992, "precision": 0.9230769230769229, "recall": 0.9999999999999999, "threshold": 12.610000000000007}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 12.610000000000007}}, "58": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.51315784454}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.51315784454}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.51315784454}}, "59": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.9421050548500016}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1.9421050548500016}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1.9421050548500016}}, "65": {"best f1 under pa": {"f1": 0.983833718244803, "precision": 0.9681818181818181, "recall": 1.0, "threshold": 1660.0}, "event-based f1 under pa with mode log": {"f1": 0.5333333333333329, "precision": 0.3636363636363636, "recall": 0.9999999999999998, "threshold": 1660.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.22222222222222193, "precision": 0.12499999999999997, "recall": 0.9999999999999989, "threshold": 1660.0}}, "71": {"best f1 under pa": {"f1": 0.9274770173646573, "precision": 0.8647619047619047, "recall": 1.0, "threshold": 4.094999999999999}, "event-based f1 under pa with mode log": {"f1": 0.8275862068965513, "precision": 0.8571428571428571, "recall": 0.8, "threshold": 7.060000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.6666666666666665, "recall": 0.7999999999999998, "threshold": 7.060000000000002}}, "164": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 195.49}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 195.49}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 195.49}}, "170": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1043.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1043.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1043.0}}, "158": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 795.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 795.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 795.0}}, "204": {"best f1 under pa": {"f1": 0.9900990099009894, "precision": 0.9803921568627451, "recall": 1.0, "threshold": 19.61}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 19.61}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 19.61}}, "166": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 119.55}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 119.55}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 119.55}}, "98": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3293.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 3293.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3293.0}}, "172": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 463.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 463.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 463.0}}, "199": {"best f1 under pa": {"f1": 0.996563573883161, "precision": 0.9931506849315068, "recall": 1.0, "threshold": 19.889999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9629629629629624, "precision": 0.9285714285714285, "recall": 0.9999999999999999, "threshold": 19.889999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 19.889999999999997}}, "67": {"best f1 under pa": {"f1": 0.9729729729729725, "precision": 0.9473684210526315, "recall": 1.0, "threshold": 1590.5}, "event-based f1 under pa with mode log": {"f1": 0.8181818181818175, "precision": 0.6923076923076922, "recall": 0.9999999999999998, "threshold": 1590.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.5999999999999999, "recall": 0.7499999999999999, "threshold": 2037.0}}, "73": {"best f1 under pa": {"f1": 0.9962406015037588, "precision": 0.9925093632958801, "recall": 1.0, "threshold": 15.529999999999994}, "event-based f1 under pa with mode log": {"f1": 0.9166666666666661, "precision": 0.846153846153846, "recall": 0.9999999999999999, "threshold": 15.529999999999994}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 15.529999999999994}}, "9": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3186.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 3186.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3186.0}}, "72": {"best f1 under pa": {"f1": 0.9747126436781602, "precision": 0.9506726457399103, "recall": 1.0, "threshold": 8.07}, "event-based f1 under pa with mode log": {"f1": 0.8695652173913037, "precision": 0.9999999999999998, "recall": 0.7692307692307692, "threshold": 27.125}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 27.125}}, "198": {"best f1 under pa": {"f1": 0.99047619047619, "precision": 0.9811320754716981, "recall": 1.0, "threshold": 18.71}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 18.71}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 18.71}}, "66": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2807.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2807.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2807.0}}, "173": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 462.5}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 462.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 462.5}}, "167": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 35.85000000000002}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 35.85000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 35.85000000000002}}, "99": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5125.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5125.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5125.0}}, "205": {"best f1 under pa": {"f1": 0.9722222222222217, "precision": 0.9459459459459459, "recall": 1.0, "threshold": 32.22}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 32.22}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999933, "precision": 0.33333333333333326, "recall": 0.9999999999999989, "threshold": 32.22}}, "128": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3.75}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3.75}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3.75}}, "29": {"best f1 under pa": {"f1": 0.985507246376811, "precision": 0.9714285714285714, "recall": 1.0, "threshold": 1.66666662693}, "event-based f1 under pa with mode log": {"f1": 0.8717948717948713, "precision": 0.7727272727272727, "recall": 1.0, "threshold": 1.66666662693}, "event-based f1 under pa with mode squeeze": {"f1": 0.7777777777777771, "precision": 0.7, "recall": 0.875, "threshold": 2.3333334922800004}}, "15": {"best f1 under pa": {"f1": 0.9936305732484072, "precision": 0.9873417721518988, "recall": 1.0, "threshold": 7.589999999999996}, "event-based f1 under pa with mode log": {"f1": 0.8947368421052626, "precision": 0.8095238095238095, "recall": 1.0, "threshold": 7.589999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.6666666666666665, "recall": 0.7999999999999998, "threshold": 13.160000000000004}}, "14": {"best f1 under pa": {"f1": 0.986666666666666, "precision": 0.9736842105263158, "recall": 1.0, "threshold": 6.809999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9189189189189183, "precision": 0.85, "recall": 1.0, "threshold": 6.809999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999993, "precision": 0.8749999999999998, "recall": 0.8749999999999998, "threshold": 10.069999999999993}}, "28": {"best f1 under pa": {"f1": 0.9953917050691239, "precision": 0.9908256880733946, "recall": 1.0, "threshold": 2.5}, "event-based f1 under pa with mode log": {"f1": 0.9743589743589738, "precision": 0.95, "recall": 1.0, "threshold": 2.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9499999999999995, "precision": 0.9047619047619048, "recall": 1.0, "threshold": 2.5}}, "101": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5918.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 5918.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5918.0}}, "117": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 7492.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 7492.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 7492.0}}, "103": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 993.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 993.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 993.0}}, "16": {"best f1 under pa": {"f1": 0.991379310344827, "precision": 0.9829059829059829, "recall": 1.0, "threshold": 20.43}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 20.43}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 20.43}}, "17": {"best f1 under pa": {"f1": 0.9925187032418947, "precision": 0.9851485148514851, "recall": 1.0, "threshold": 8.29}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888883, "precision": 0.7999999999999999, "recall": 0.9999999999999999, "threshold": 8.29}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 17.099999999999998}}, "102": {"best f1 under pa": {"f1": 0.9844559585492223, "precision": 0.9693877551020408, "recall": 1.0, "threshold": 478.5}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.8235294117647058, "recall": 0.9999999999999999, "threshold": 478.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.8235294117647052, "precision": 0.6999999999999998, "recall": 0.9999999999999999, "threshold": 478.5}}, "116": {"best f1 under pa": {"f1": 0.9993131868131861, "precision": 0.9986273164035689, "recall": 1.0, "threshold": 1054.0}, "event-based f1 under pa with mode log": {"f1": 0.6999999999999994, "precision": 0.5384615384615384, "recall": 0.9999999999999999, "threshold": 1054.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.2499999999999997, "precision": 0.14285714285714285, "recall": 0.9999999999999989, "threshold": 1054.0}}, "112": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 371.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 371.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 371.0}}, "106": {"best f1 under pa": {"f1": 0.9915254237288129, "precision": 0.9831932773109243, "recall": 1.0, "threshold": 351.0}, "event-based f1 under pa with mode log": {"f1": 0.9473684210526311, "precision": 0.9, "recall": 1.0, "threshold": 351.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.9999999999999998, "recall": 0.8888888888888887, "threshold": 568.0}}, "13": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 8.36}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 8.36}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 8.36}}, "12": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 13.160000000000004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 13.160000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 13.160000000000004}}, "107": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 441.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 441.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 441.0}}, "113": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 502.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 502.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 502.0}}, "105": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 568.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 568.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 568.0}}, "111": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1011.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1011.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1011.0}}, "10": {"best f1 under pa": {"f1": 0.9932885906040263, "precision": 0.9866666666666667, "recall": 1.0, "threshold": 1259.0}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 1259.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 1259.0}}, "38": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.82}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.82}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 9.82}}, "110": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 419.5}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 419.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 419.5}}, "104": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 531.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 531.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 531.0}}}