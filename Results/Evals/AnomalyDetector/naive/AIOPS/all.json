{"c02607e8-7399-3dde-9d28-8a8da5e5d251": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.05050079600278509}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 0.05050079600278509}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 0.05050079600278509}}, "a8c06b47-cc41-3738-9110-12df0ee4c721": {"best f1 under pa": {"f1": 0.8991354466858784, "precision": 0.9285714285714286, "recall": 0.8715083798882681, "threshold": 4.55269238064}, "event-based f1 under pa with mode log": {"f1": 0.8108108108108103, "precision": 0.9375, "recall": 0.7142857142857143, "threshold": 7.912265378762}, "event-based f1 under pa with mode squeeze": {"f1": 0.777777777777777, "precision": 0.8749999999999998, "recall": 0.6999999999999998, "threshold": 7.912265378762}}, "6d1114ae-be04-3c46-b5aa-be1a003a57cd": {"best f1 under pa": {"f1": 0.8118486012068014, "precision": 0.9273182957393483, "recall": 0.7219512195121951, "threshold": 24.840000000000003}, "event-based f1 under pa with mode log": {"f1": 0.8279069767441853, "precision": 0.9270833333333334, "recall": 0.7478991596638656, "threshold": 35.85000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.8387096774193543, "precision": 0.975, "recall": 0.7358490566037735, "threshold": 44.66999999999999}}, "1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0": {"best f1 under pa": {"f1": 0.9304871373836885, "precision": 0.9320175438596491, "recall": 0.9289617486338798, "threshold": 5.469999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9083333333333328, "precision": 0.9819819819819819, "recall": 0.8449612403100775, "threshold": 8.630000000000003}, "event-based f1 under pa with mode squeeze": {"f1": 0.8952380952380946, "precision": 0.9591836734693877, "recall": 0.8392857142857143, "threshold": 8.630000000000003}}, "54350a12-7a9d-3ca8-b81f-f886b9d156fd": {"best f1 under pa": {"f1": 0.9463087248322142, "precision": 0.8980891719745223, "recall": 1.0, "threshold": 2.783788141541}, "event-based f1 under pa with mode log": {"f1": 0.6521739130434777, "precision": 0.4838709677419355, "recall": 0.9999999999999999, "threshold": 2.783788141541}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999994, "precision": 0.7499999999999999, "recall": 0.49999999999999994, "threshold": 5.95983577779}}, "301c70d8-1630-35ac-8f96-bc1b6f4359ea": {"best f1 under pa": {"f1": 0.9377483443708604, "precision": 0.9053708439897699, "recall": 0.9725274725274725, "threshold": 0.02758618239712593}, "event-based f1 under pa with mode log": {"f1": 0.8345323741007188, "precision": 0.7945205479452054, "recall": 0.8787878787878788, "threshold": 0.03266072667405101}, "event-based f1 under pa with mode squeeze": {"f1": 0.7692307692307686, "precision": 0.78125, "recall": 0.7575757575757576, "threshold": 0.0399000249970675}}, "8723f0fb-eaef-32e6-b372-6034c9c04b80": {"best f1 under pa": {"f1": 0.9132841328413277, "precision": 0.9455587392550143, "recall": 0.8831400535236396, "threshold": 25.600000000000023}, "event-based f1 under pa with mode log": {"f1": 0.8188405797101443, "precision": 0.8188405797101449, "recall": 0.8188405797101449, "threshold": 29.28}, "event-based f1 under pa with mode squeeze": {"f1": 0.7863247863247858, "precision": 0.8363636363636363, "recall": 0.7419354838709677, "threshold": 35.19999999999999}}, "e0747cad-8dc8-38a9-a9ab-855b61f5551d": {"best f1 under pa": {"f1": 0.9851632047477739, "precision": 0.9707602339181286, "recall": 1.0, "threshold": 0.5521484919999999}, "event-based f1 under pa with mode log": {"f1": 0.8999999999999995, "precision": 1.0, "recall": 0.8181818181818182, "threshold": 0.6625781919999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.9999999999999998, "recall": 0.7999999999999998, "threshold": 0.6625781919999998}}, "57051487-3a40-3828-9084-a12f7f23ee38": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 545.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 545.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 545.25}}, "da10a69f-d836-3baa-ad40-3e548ecf1fbd": {"best f1 under pa": {"f1": 0.9264271903938124, "precision": 0.8727830505224811, "recall": 0.9870974737070367, "threshold": 0.043364386219}, "event-based f1 under pa with mode log": {"f1": 0.6843657817109139, "precision": 0.8923076923076924, "recall": 0.5550239234449761, "threshold": 0.128707901131}, "event-based f1 under pa with mode squeeze": {"f1": 0.671532846715328, "precision": 0.7666666666666667, "recall": 0.5974025974025974, "threshold": 0.128707901131}}, "ab216663-dcc2-3a24-b1ee-2c3e550e06c9": {"best f1 under pa": {"f1": 0.5589225589225585, "precision": 1.0, "recall": 0.3878504672897196, "threshold": 0.153806401243}, "event-based f1 under pa with mode log": {"f1": 0.7450980392156857, "precision": 1.0, "recall": 0.59375, "threshold": 0.153806401243}, "event-based f1 under pa with mode squeeze": {"f1": 0.7826086956521733, "precision": 0.9999999999999998, "recall": 0.6428571428571428, "threshold": 0.153806401243}}, "f0932edd-6400-3e63-9559-0a9860a1baa9": {"best f1 under pa": {"f1": 0.9798583136546737, "precision": 0.9647155361050328, "recall": 0.9954840530623765, "threshold": 7.0625}, "event-based f1 under pa with mode log": {"f1": 0.8178438661710032, "precision": 0.8870967741935484, "recall": 0.7586206896551724, "threshold": 10.0625}, "event-based f1 under pa with mode squeeze": {"f1": 0.7603305785123962, "precision": 0.7666666666666667, "recall": 0.7540983606557377, "threshold": 10.0625}}, "0efb375b-b902-3661-ab23-9a0bb799f4e3": {"best f1 under pa": {"f1": 0.9285714285714279, "precision": 0.9578947368421052, "recall": 0.900990099009901, "threshold": 0.0291068734442107}, "event-based f1 under pa with mode log": {"f1": 0.7857142857142851, "precision": 0.7333333333333333, "recall": 0.846153846153846, "threshold": 0.0291068734442107}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.7999999999999998, "recall": 0.6666666666666665, "threshold": 0.039318940734362096}}, "c69a50cf-ee03-3bd7-831e-407d36c7ee91": {"best f1 under pa": {"f1": 0.935403104656985, "precision": 0.935871743486974, "recall": 0.934934934934935, "threshold": 5.25}, "event-based f1 under pa with mode log": {"f1": 0.9307692307692302, "precision": 0.983739837398374, "recall": 0.8832116788321168, "threshold": 7.329999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9203539823008844, "precision": 0.9629629629629629, "recall": 0.8813559322033898, "threshold": 7.329999999999998}}, "ba5f3328-9f3f-3ff5-a683-84437d16d554": {"best f1 under pa": {"f1": 0.9860100727476212, "precision": 0.9975937721160651, "recall": 0.9746922970543493, "threshold": 22.77000000000001}, "event-based f1 under pa with mode log": {"f1": 0.9366515837104068, "precision": 0.9857142857142858, "recall": 0.8922413793103449, "threshold": 26.47999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9146341463414629, "precision": 0.9615384615384616, "recall": 0.872093023255814, "threshold": 26.47999999999999}}, "adb2fde9-8589-3f5b-a410-5fe14386c7af": {"best f1 under pa": {"f1": 0.9162156310889797, "precision": 0.8453855373551737, "recall": 1.0, "threshold": 4.859999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9209809264305171, "precision": 0.9657142857142857, "recall": 0.8802083333333334, "threshold": 9.000000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9230769230769225, "precision": 0.9285714285714286, "recall": 0.9176470588235294, "threshold": 9.000000000000004}}, "a07ac296-de40-3a7c-8df3-91f642cc14d0": {"best f1 under pa": {"f1": 0.8558246828143017, "precision": 0.8554957724827056, "recall": 0.8561538461538462, "threshold": 14.824999999999989}, "event-based f1 under pa with mode log": {"f1": 0.7640449438202241, "precision": 0.9902912621359223, "recall": 0.6219512195121951, "threshold": 30.159999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.7787610619469021, "precision": 0.9777777777777777, "recall": 0.6470588235294118, "threshold": 30.159999999999997}}, "4d2af31a-9916-3d9f-8a8e-8a268a48c095": {"best f1 under pa": {"f1": 0.9879865449303215, "precision": 0.9974126778783958, "recall": 0.9787369089178038, "threshold": 1.33333337307}, "event-based f1 under pa with mode log": {"f1": 0.8639999999999994, "precision": 0.8709677419354839, "recall": 0.8571428571428571, "threshold": 1.33333337307}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999995, "precision": 0.8181818181818182, "recall": 0.782608695652174, "threshold": 1.40000009536}}, "ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa": {"best f1 under pa": {"f1": 0.9894798363530093, "precision": 0.9791787160208213, "recall": 1.0, "threshold": 0.6499999761599999}, "event-based f1 under pa with mode log": {"f1": 0.8235294117647054, "precision": 0.8596491228070176, "recall": 0.7903225806451613, "threshold": 0.899999976158}, "event-based f1 under pa with mode squeeze": {"f1": 0.7777777777777771, "precision": 0.9333333333333332, "recall": 0.6666666666666666, "threshold": 1.3315787315400005}}, "55f8b8b8-b659-38df-b3df-e4a5a8a54bc9": {"best f1 under pa": {"f1": 0.9870149945895805, "precision": 0.9877784653465347, "recall": 0.986252703120173, "threshold": 19.919999999999987}, "event-based f1 under pa with mode log": {"f1": 0.9194805194805188, "precision": 0.9365079365079365, "recall": 0.9030612244897959, "threshold": 25.689999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9051094890510943, "precision": 0.9538461538461539, "recall": 0.8611111111111112, "threshold": 29.930000000000007}}, "431a8542-c468-3988-a508-3afd06a218da": {"best f1 under pa": {"f1": 0.9961364389005404, "precision": 0.9923026171101825, "recall": 1.0, "threshold": 0.8125}, "event-based f1 under pa with mode log": {"f1": 0.9768574908647985, "precision": 0.9685990338164251, "recall": 0.9852579852579852, "threshold": 1.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9631578947368417, "precision": 0.973404255319149, "recall": 0.953125, "threshold": 1.2941176891299997}}, "42d6616d-c9c5-370a-a8ba-17ead74f3114": {"best f1 under pa": {"f1": 0.9249729729729725, "precision": 0.8864484044757563, "recall": 0.966998191681736, "threshold": 5.190000000000001}, "event-based f1 under pa with mode log": {"f1": 0.8928571428571423, "precision": 0.9541984732824428, "recall": 0.8389261744966443, "threshold": 11.579999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.8959999999999994, "precision": 0.9032258064516129, "recall": 0.8888888888888888, "threshold": 11.579999999999998}}, "43115f2a-baeb-3b01-96f7-4ea14188343c": {"best f1 under pa": {"f1": 0.9372909698996651, "precision": 0.9357262103505843, "recall": 0.9388609715242882, "threshold": 4.379999999999999}, "event-based f1 under pa with mode log": {"f1": 0.7049808429118768, "precision": 0.6618705035971223, "recall": 0.7540983606557377, "threshold": 4.82}, "event-based f1 under pa with mode squeeze": {"f1": 0.5974025974025969, "precision": 0.8846153846153846, "recall": 0.45098039215686275, "threshold": 8.61}}, "9c639a46-34c8-39bc-aaf0-9144b37adfc8": {"best f1 under pa": {"f1": 0.9015777610818928, "precision": 0.9259259259259259, "recall": 0.8784773060029283, "threshold": 3.969999999999999}, "event-based f1 under pa with mode log": {"f1": 0.6749226006191945, "precision": 0.7171052631578947, "recall": 0.6374269005847953, "threshold": 4.660000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.5590062111801237, "precision": 0.5487804878048781, "recall": 0.569620253164557, "threshold": 4.879999999999999}}, "05f10d3a-239c-3bef-9bdc-a2feeb0037aa": {"best f1 under pa": {"f1": 0.9881543752388224, "precision": 0.9832699619771863, "recall": 0.9930875576036866, "threshold": 6.700000000000003}, "event-based f1 under pa with mode log": {"f1": 0.9671052631578942, "precision": 0.9735099337748344, "recall": 0.9607843137254902, "threshold": 8.189999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.944881889763779, "precision": 0.9375, "recall": 0.9523809523809523, "threshold": 8.189999999999998}}, "7103fa0f-cac4-314f-addc-866190247439": {"best f1 under pa": {"f1": 0.9344729344729339, "precision": 0.8913043478260869, "recall": 0.9820359281437125, "threshold": 82.8000030518}, "event-based f1 under pa with mode log": {"f1": 0.8831168831168824, "precision": 0.9444444444444444, "recall": 0.8292682926829268, "threshold": 127.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8648648648648642, "precision": 1.0, "recall": 0.7619047619047619, "threshold": 188.800003052}}, "6efa3a07-4544-34a0-b921-a155bd1a05e8": {"best f1 under pa": {"f1": 0.8533590175776541, "precision": 0.8103658536585366, "recall": 0.9011696897779284, "threshold": 13.57}, "event-based f1 under pa with mode log": {"f1": 0.39215686274509753, "precision": 0.41379310344827586, "recall": 0.37267080745341613, "threshold": 29.65}, "event-based f1 under pa with mode squeeze": {"f1": 0.24451410658307163, "precision": 0.195, "recall": 0.3277310924369748, "threshold": 30.429999999999993}}, "6a757df4-95e5-3357-8406-165e2bd49360": {"best f1 under pa": {"f1": 0.9157303370786511, "precision": 0.916639100231558, "recall": 0.9148233740508419, "threshold": 0.8888889402119999}, "event-based f1 under pa with mode log": {"f1": 0.5714285714285711, "precision": 0.9473684210526315, "recall": 0.4090909090909091, "threshold": 3.55555546283}, "event-based f1 under pa with mode squeeze": {"f1": 0.6363636363636357, "precision": 0.8749999999999998, "recall": 0.49999999999999994, "threshold": 3.55555546283}}, "847e8ecc-f8d2-3a93-9107-f367a0aab37d": {"best f1 under pa": {"f1": 0.9902912621359217, "precision": 0.9807692307692307, "recall": 1.0, "threshold": 7.34}, "event-based f1 under pa with mode log": {"f1": 0.9342560553633212, "precision": 0.8940397350993378, "recall": 0.9782608695652174, "threshold": 7.729999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9189189189189184, "precision": 0.9272727272727272, "recall": 0.9107142857142857, "threshold": 9.719999999999999}}}