def strange_sort_list(lst):
    '''
    Given list of integers, return list in strange order.
    Strange sorting, is when you start with the minimum value,
    then maximum of the remaining integers, then minimum and so on.

    Examples:
    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]
    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]
    strange_sort_list([]) == []
    '''
    lst = sorted(lst)
    return lst

import os
os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'
os.environ['TRACE_CUSTOMLLM_URL'] = "http://21.91.109.192:30000/v1"
os.environ['TRACE_CUSTOMLLM_MODEL'] = "deepseek-ai/DeepSeek-V3"
os.environ['TRACE_CUSTOMLLM_API_KEY'] = "sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt"

strange_sort_list([1, 2, 3, 4])

from opto.trace import node, bundle

@bundle(trainable=True)
def strange_sort_list(lst):
    '''
    Given list of integers, return list in strange order.
    Strange sorting, is when you start with the minimum value,
    then maximum of the remaining integers, then minimum and so on.

    Examples:
    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]
    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]
    strange_sort_list([]) == []
    '''
    lst = sorted(lst)
    return lst

test_input = [1, 2, 3, 4]
test_output = strange_sort_list(test_input)
print(test_output)

def get_feedback(predict, target):
    if predict == target:
        return "test case passed!"
    else:
        return "test case failed!"

from opto.optimizers import OptoPrime
from opto import trace

test_ground_truth = [1, 4, 2, 3]
test_input = [1, 2, 3, 4]

epoch = 2

optimizer = OptoPrime(strange_sort_list.parameters())

for i in range(epoch):
    print(f"Training Epoch {i}")
    try:
        test_output = strange_sort_list(test_input)
        feedback = get_feedback(test_output, test_ground_truth)
    except trace.ExecutionError as e:
        feedback = e.exception_node.data
        test_output = e.exception_node
    
    correctness = test_output.eq(test_ground_truth)
    
    if correctness:
        break

    optimizer.zero_feedback()
    optimizer.backward(correctness, feedback)
    optimizer.step()

def render_opt_step(step_idx, optimizer, no_trace_graph=False, no_improvement=False):
    import json
    import re
    from IPython.display import display, HTML

    idx = step_idx
    llm_response = json.loads(optimizer.log[idx]["response"])
    r1 = llm_response["reasoning"]

    if llm_response.get("suggestion"):
        a1 = "".join(
            [
                f"{var_name}:\n\n{var_body}\n\n"
                for var_name, var_body in llm_response["suggestion"].items()
            ]
        )
    elif llm_response.get("answer") is not None:
        a1 = llm_response["answer"]
    else:
        a1 = "<ERROR> NULL/INVALID RESPONSE"

    pi = optimizer.summary_log[idx]["problem_instance"]  # full
    f1 = pi.feedback

    masked = ["#Feedback", "#Others", "#Instruction"]
    pi = optimizer.problem_instance(optimizer.summary_log[idx]["summary"], mask=masked)

    # a hack to remove "#Feedback:" because it has a colon
    pi = str(pi)
    pi = pi.replace("#Feedback:", "#Feedback")

    for m in masked:
        pi = pi.replace(m + "\n", "")

    # a quick processing to reduce multiple empty lines to one
    pi = re.sub(r"\n\s*\n", "\n\n", pi)
    g1 = pi

    html_template = f"""
    <style>
        :root {{
            --text-color: #1c1c1c;
            --bg-color: #ffffff;
            --trace-bg: #e0e0e0;
            --trace-border: #9e9e9e;
            --feedback-bg: #ffb3ba;
            --feedback-border: #ff6b6b;
            --reason-bg: #baffc9;
            --reason-border: #4caf50;
            --improve-bg: #ffffff;
            --improve-border: #4d9de0;
        }}
        @media (prefers-color-scheme: dark) {{
            :root {{
                --text-color: #e0e0e0;
                --bg-color: #121212;
                --trace-bg: #2a2a2a;
                --trace-border: #555555;
                --feedback-bg: #5c2b30;
                --feedback-border: #ff6b6b;
                --reason-bg: #1e3a2b;
                --reason-border: #4caf50;
                --improve-bg: #121212;
                --improve-border: #4d9de0;
            }}
        }}
    </style>

    <div style="font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);">
    """

    if not no_trace_graph:
        html_template += f"""
        <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
            <div style="flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;">
                <p><b>Trace Graph</b></p>
                <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);">{g1}</pre>
            </div>
            <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);">
                g<sub>{idx}</sub>
            </div>
        </div>
        """

    html_template += f"""
    <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
        <div style="flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;">
            <p style="margin: 0;"><b>Feedback: </b>{f1}</p>
        </div>
        <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);">
            f<sub>{idx}</sub>
        </div>
    </div>

    <div style="display: flex; align-items: stretch; margin-bottom: 10px;">
        <div style="flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;">
            <p style="margin: 0;"><b>Reasoning: </b>{r1}</p>
        </div>
        <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);">
            r<sub>{idx + 1}</sub>
        </div>
    </div>
    """

    if not no_improvement:
        html_template += f"""
        <div style="display: flex; align-items: stretch; margin-bottom: 20px;">
            <div style="flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;">
                <p><b>Improvement</b></p>
                <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);">{a1}</pre>
            </div>
            <div style="width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);">
                a<sub>{idx + 1}</sub>
            </div>
        </div>
        """

    html_template += "</div>"

    display(HTML(html_template))

render_opt_step(0, optimizer)

import numpy as np
a = np.array([1, 2, 3, 4])
b = np.array([5, 6, 7, 8])
np.min([a, b],axis =0)