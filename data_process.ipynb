import json
from datetime import datetime
import hashlib

with open('raw_data/2025_8_12_14_28.json', 'r') as f:
    data = json.load(f) 

data

data.keys()

len(data['dataPoints'])

data['dataPoints'][0].keys()

print(data['dataPoints'][0])

for points in data['dataPoints']:
    print(points['appMark'])

for points in data['dataPoints']:
    print(points['metricName'])

for points in data['dataPoints']:
    print(points['dimensions'])

for points in data['dataPoints']:
    points['ts'] = {}
    points['ts']['time'] = [datetime.fromtimestamp(timestamp).strftime("%m/%d/%Y %H:%M:%S") for timestamp in list(range(points['beginTimestamp'], points['endTimestamp']+1, 60))]
    points['ts']['value'] = points['value']
    del points['value']
    points['beginTimestamp'] = datetime.fromtimestamp(points['beginTimestamp']).strftime("%m/%d/%Y %H:%M:%S")
    points['endTimestamp'] = datetime.fromtimestamp(points['endTimestamp']).strftime("%m/%d/%Y %H:%M:%S")

    json_str = json.dumps(points, sort_keys=True)
    with open('processed_data/'+hashlib.sha256(json_str.encode()).hexdigest()+'.json', 'w') as f:
        json.dump(points, f)

points

(data['dataPoints'][0]['endTimestamp'] - data['dataPoints'][0]['beginTimestamp'])/60