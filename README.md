# Comprehensive Online Anomaly Detection System

A modular, configurable anomaly detection system designed for **real-time/streaming** time series processing. The system processes data point-by-point, ensuring no future data leakage - perfect for real-time anomaly detection applications.

## Key Features

- **🔄 Online Processing**: Point-by-point processing with no future data access
- **🏗️ Modular Architecture**: Four independent, configurable modules
- **⚡ Real-time Ready**: Suitable for streaming data and real-time applications
- **🎯 Multiple Algorithms**: 3+ options per module for different use cases
- **⚙️ Flexible Configuration**: Easy-to-use configuration system
- **🧪 Comprehensive Testing**: Extensive test suite validating online behavior
- **📈 Performance Optimized**: Options for speed vs. accuracy trade-offs
- **📚 Well Documented**: Complete examples and usage guides

## Quick Start

### Batch Processing (Simulates Online)
```python
from anomaly_detection_system import detect_anomalies
import numpy as np

# Generate sample data
data = np.random.normal(0, 1, 100)
data[25] = 5.0  # Add anomaly

# Detect anomalies (processes point-by-point internally)
scores, flags = detect_anomalies(data)

print(f"Anomalies detected: {np.sum(flags)}")
print(f"Anomaly positions: {np.where(flags)[0]}")
```

### True Streaming Processing
```python
from anomaly_detection_system import create_online_detector

# Create online detector
detector = create_online_detector()

# Process data points one at a time (real-time)
for value in data:
    score, is_anomaly = detector.process_point(value)
    if is_anomaly:
        print(f"Anomaly detected: value={value:.2f}, score={score:.3f}")
```

## System Architecture

The system processes data in **online/streaming fashion** where each time step `t` only has access to historical data from time steps `[0, 1, ..., t-1]`. No future data is ever used, making it suitable for real-time applications.

### 1. Sampler Module (Historical Data Sampling)
Samples from available historical data `[0, 1, ..., t-1]` when processing time step `t` to reduce computational overhead.

**Options:**
- `identity`: Use all historical data (default)
- `uniform`: Uniform sampling from historical data with configurable step size
- `adaptive`: Adaptive sampling based on historical data variance

**Configuration:**
```python
config = {
    "sampler": {
        "method": "uniform",
        "step_size": 2,
        "max_history": 200  # Limit historical data size
    }
}
```

### 2. Denoiser Module (Online Signal Smoothing)
Applies denoising to the current value using sampled historical data. Maintains state across time steps.

**Options:**
- `identity`: No denoising (default)
- `moving_average`: Moving average using recent historical values
- `exponential`: Exponential smoothing with state memory

**Configuration:**
```python
config = {
    "denoiser": {
        "method": "moving_average",
        "window_size": 5
    }
}
```

### 3. Detector Module (Online Anomaly Detection)
Detects anomalies in the current value using only historical data. Maintains running statistics.

**Options:**
- `zscore`: Statistical threshold-based detection using historical statistics (default)
- `iqr`: Interquartile range based detection using historical percentiles
- `changepoint`: Change point detection using recent value buffer
- `diff`: Difference-based detection using most recent historical value

**Configuration:**
```python
config = {
    "detector": {
        "method": "zscore",
        "threshold": 3.0,
        "window_size": 50,  # Use recent 50 historical points
        "min_samples": 10   # Minimum historical data required
    }
}

# Example: Diff-based detection for rapid change detection
config = {
    "detector": {
        "method": "diff",
        "threshold": 2.0    # Threshold for absolute difference normalization
    }
}
```

### 4. Filter Module (Online Post-processing)
Filters current detection results using historical context. Maintains state for temporal and pattern filtering.

**Options:**
- `identity`: No filtering (default)
- `temporal`: Suppress anomalies too close in time to previous ones
- `pattern_matching`: Suppress anomalies matching previously seen patterns

**Configuration:**
```python
config = {
    "filter": {
        "method": "temporal",
        "min_distance": 5  # Minimum time steps between anomalies
    }
}
```

## Complete Configuration Example

```python
config = {
    "sampler": {
        "method": "adaptive",
        "window_size": 20,
        "variance_threshold": 0.1
    },
    "denoiser": {
        "method": "moving_average",
        "window_size": 7
    },
    "detector": {
        "method": "zscore",
        "threshold": 2.5,
        "window_size": 50
    },
    "filter": {
        "method": "temporal",
        "min_distance": 10
    }
}

scores, flags = detect_anomalies(data, config)
```

## Real-World Scenarios

### High-Frequency Sensor Data
```python
sensor_config = {
    "sampler": {"method": "uniform", "step_size": 5},
    "denoiser": {"method": "moving_average", "window_size": 10},
    "detector": {"method": "zscore", "threshold": 3.0},
    "filter": {"method": "temporal", "min_distance": 20}
}
```

### Financial Time Series
```python
finance_config = {
    "sampler": {"method": "adaptive", "window_size": 20},
    "denoiser": {"method": "exponential", "alpha": 0.1},
    "detector": {"method": "iqr", "multiplier": 2.0},
    "filter": {"method": "pattern_matching", "pattern_window": 15}
}
```

### System Monitoring
```python
system_config = {
    "sampler": {"method": "identity"},
    "denoiser": {"method": "moving_average", "window_size": 5},
    "detector": {"method": "changepoint", "window_size": 30},
    "filter": {"method": "temporal", "min_distance": 10}
}
```

### Manufacturing Quality Control
```python
quality_config = {
    "sampler": {"method": "identity"},
    "denoiser": {"method": "identity"},
    "detector": {"method": "zscore", "threshold": 2.0, "window_size": 50},
    "filter": {"method": "identity"}
}
```

## Performance Optimization

### For Speed
- Use uniform sampling with larger step sizes
- Skip denoising (`identity`)
- Use simple detectors (global z-score or diff)
- Skip filtering (`identity`)
- **New**: Diff detection is ~20x faster than statistical methods

### For Accuracy
- Keep all data (`identity` sampling)
- Use appropriate denoising for your data
- Choose detector matching your anomaly type
- Use filtering to reduce false positives

### For Memory Efficiency
- Use aggressive uniform sampling
- Limit pattern matching history
- Process data in chunks

### Vectorized Optimizations
The system now uses vectorized NumPy operations for:
- Statistical calculations (mean, std, percentiles)
- Array indexing and slicing operations
- Correlation computations in pattern matching
- Mathematical operations in all detection methods

These optimizations provide significant performance improvements while maintaining identical results.

## API Reference

### Main Function

```python
detect_anomalies(data, config=None) -> Tuple[np.ndarray, np.ndarray]
```

**Parameters:**
- `data`: One-dimensional time series (numpy array or list)
- `config`: Configuration dictionary (optional)

**Returns:**
- `anomaly_scores`: Float array (0-1) indicating anomaly likelihood
- `binary_flags`: Boolean array (True = anomaly detected)

### Module Classes

All modules inherit from `BaseModule` and implement the `process()` method:

- `SamplerModule(method, **params)`
- `DenoiserModule(method, **params)`
- `DetectorModule(method, **params)`
- `FilterModule(method, **params)`

## Testing

Run the comprehensive test suite:

```bash
python test_anomaly_detection.py
```

The test suite includes:
- Basic functionality tests
- Edge case handling
- Module option validation
- Performance comparisons
- Synthetic data validation
- Visual inspection tests

## Examples

Run example usage scenarios:

```bash
python example_usage.py
```

This demonstrates:
- Basic usage patterns
- Module configurations
- Real-world scenarios
- Performance optimization
- Visualization examples

## Design Choices and Trade-offs

### Sampler Module
- **Identity**: Maximum accuracy, highest computational cost
- **Uniform**: Fastest processing, may miss short anomalies
- **Adaptive**: Balanced approach, preserves important regions

### Denoiser Module
- **Identity**: Preserves all signal details, sensitive to noise
- **Moving Average**: Good noise reduction, may blur sharp features
- **Exponential**: Adaptive smoothing, good for trending data

### Detector Module
- **Z-score**: Fast, assumes normal distribution, good for outliers
- **IQR**: Robust to distribution, good for extreme outliers
- **Change Point**: Detects regime changes, good for drift

### Filter Module
- **Identity**: No false positive reduction, preserves all detections
- **Temporal**: Reduces clustered alerts, may miss real clusters
- **Pattern Matching**: Learns from history, may over-suppress

## Requirements

- Python 3.7+
- NumPy
- Matplotlib (for visualization examples)

## Installation

Simply copy the following files to your project:
- `anomaly_detection_system.py` (main system)
- `test_anomaly_detection.py` (test suite)
- `example_usage.py` (examples and documentation)

## License

This anomaly detection system is provided as-is for educational and research purposes.

## Online Processing Guarantees

This system is specifically designed for **real-time anomaly detection** with the following guarantees:

### ✅ No Future Data Leakage
- At time step `t`, only data from time steps `[0, 1, ..., t-1]` is accessible
- No future data is ever used in any module
- Suitable for true real-time streaming applications

### ✅ Consistent Results
- Batch processing (simulates online) and true streaming processing produce identical results
- Deterministic behavior given the same input sequence
- Comprehensive test suite validates online behavior

### ✅ Memory Efficient
- Configurable historical data limits prevent memory growth
- Adaptive sampling reduces computational overhead
- State management optimized for long-running streams

### ✅ Real-time Ready
- Point-by-point processing with minimal latency
- Stateful modules maintain context across time steps
- Suitable for high-frequency data streams

## Use Cases

Perfect for:
- **IoT Sensor Monitoring**: Real-time detection of sensor anomalies
- **Financial Trading**: Live market anomaly detection
- **System Monitoring**: Real-time server/application monitoring
- **Manufacturing**: Live quality control and process monitoring
- **Network Security**: Real-time intrusion detection

## Contributing

Feel free to extend the system with additional modules or detection algorithms following the established patterns. When adding new modules, ensure they maintain the online processing guarantees.