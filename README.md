# Comprehensive Anomaly Detection System

A modular, configurable anomaly detection system for time series data with four main components: Sampler, Denoiser, Detector, and Filter.

## Features

- **Modular Architecture**: Four independent, configurable modules
- **Multiple Algorithms**: 3+ options per module for different use cases
- **Flexible Configuration**: Easy-to-use configuration system
- **Comprehensive Testing**: Extensive test suite with synthetic and real-world scenarios
- **Performance Optimized**: Options for speed vs. accuracy trade-offs
- **Well Documented**: Complete examples and usage guides

## Quick Start

```python
from anomaly_detection_system import detect_anomalies
import numpy as np

# Generate sample data
data = np.random.normal(0, 1, 100)
data[25] = 5.0  # Add anomaly

# Detect anomalies with default settings
scores, flags = detect_anomalies(data)

print(f"Anomalies detected: {np.sum(flags)}")
print(f"Anomaly positions: {np.where(flags)[0]}")
```

## System Architecture

### 1. Sampler Module (Data Compression)
Reduces computational overhead by compressing input data.

**Options:**
- `identity`: No sampling (default)
- `uniform`: Uniform downsampling with configurable step size
- `adaptive`: Adaptive sampling based on data variance

**Configuration:**
```python
config = {
    "sampler": {
        "method": "uniform",
        "step_size": 2
    }
}
```

### 2. Denoiser Module (Signal Smoothing)
Smooths the time series to reduce noise impact on detection.

**Options:**
- `identity`: No denoising (default)
- `moving_average`: Moving average with configurable window size
- `exponential`: Exponential smoothing with alpha parameter

**Configuration:**
```python
config = {
    "denoiser": {
        "method": "moving_average",
        "window_size": 5
    }
}
```

### 3. Detector Module (Anomaly Detection Core)
Identifies numerical change points and assigns anomaly scores.

**Options:**
- `zscore`: Statistical threshold-based detection (default)
- `iqr`: Interquartile range based detection
- `changepoint`: Change point detection algorithms

**Configuration:**
```python
config = {
    "detector": {
        "method": "zscore",
        "threshold": 3.0,
        "window_size": None  # Global statistics
    }
}
```

### 4. Filter Module (Post-processing)
Filters out false positives and known patterns.

**Options:**
- `identity`: No filtering (default)
- `temporal`: Suppress anomalies too close in time
- `pattern_matching`: Suppress previously seen patterns

**Configuration:**
```python
config = {
    "filter": {
        "method": "temporal",
        "min_distance": 5
    }
}
```

## Complete Configuration Example

```python
config = {
    "sampler": {
        "method": "adaptive",
        "window_size": 20,
        "variance_threshold": 0.1
    },
    "denoiser": {
        "method": "moving_average",
        "window_size": 7
    },
    "detector": {
        "method": "zscore",
        "threshold": 2.5,
        "window_size": 50
    },
    "filter": {
        "method": "temporal",
        "min_distance": 10
    }
}

scores, flags = detect_anomalies(data, config)
```

## Real-World Scenarios

### High-Frequency Sensor Data
```python
sensor_config = {
    "sampler": {"method": "uniform", "step_size": 5},
    "denoiser": {"method": "moving_average", "window_size": 10},
    "detector": {"method": "zscore", "threshold": 3.0},
    "filter": {"method": "temporal", "min_distance": 20}
}
```

### Financial Time Series
```python
finance_config = {
    "sampler": {"method": "adaptive", "window_size": 20},
    "denoiser": {"method": "exponential", "alpha": 0.1},
    "detector": {"method": "iqr", "multiplier": 2.0},
    "filter": {"method": "pattern_matching", "pattern_window": 15}
}
```

### System Monitoring
```python
system_config = {
    "sampler": {"method": "identity"},
    "denoiser": {"method": "moving_average", "window_size": 5},
    "detector": {"method": "changepoint", "window_size": 30},
    "filter": {"method": "temporal", "min_distance": 10}
}
```

### Manufacturing Quality Control
```python
quality_config = {
    "sampler": {"method": "identity"},
    "denoiser": {"method": "identity"},
    "detector": {"method": "zscore", "threshold": 2.0, "window_size": 50},
    "filter": {"method": "identity"}
}
```

## Performance Optimization

### For Speed
- Use uniform sampling with larger step sizes
- Skip denoising (`identity`)
- Use simple detectors (global z-score)
- Skip filtering (`identity`)

### For Accuracy
- Keep all data (`identity` sampling)
- Use appropriate denoising for your data
- Choose detector matching your anomaly type
- Use filtering to reduce false positives

### For Memory Efficiency
- Use aggressive uniform sampling
- Limit pattern matching history
- Process data in chunks

## API Reference

### Main Function

```python
detect_anomalies(data, config=None) -> Tuple[np.ndarray, np.ndarray]
```

**Parameters:**
- `data`: One-dimensional time series (numpy array or list)
- `config`: Configuration dictionary (optional)

**Returns:**
- `anomaly_scores`: Float array (0-1) indicating anomaly likelihood
- `binary_flags`: Boolean array (True = anomaly detected)

### Module Classes

All modules inherit from `BaseModule` and implement the `process()` method:

- `SamplerModule(method, **params)`
- `DenoiserModule(method, **params)`
- `DetectorModule(method, **params)`
- `FilterModule(method, **params)`

## Testing

Run the comprehensive test suite:

```bash
python test_anomaly_detection.py
```

The test suite includes:
- Basic functionality tests
- Edge case handling
- Module option validation
- Performance comparisons
- Synthetic data validation
- Visual inspection tests

## Examples

Run example usage scenarios:

```bash
python example_usage.py
```

This demonstrates:
- Basic usage patterns
- Module configurations
- Real-world scenarios
- Performance optimization
- Visualization examples

## Design Choices and Trade-offs

### Sampler Module
- **Identity**: Maximum accuracy, highest computational cost
- **Uniform**: Fastest processing, may miss short anomalies
- **Adaptive**: Balanced approach, preserves important regions

### Denoiser Module
- **Identity**: Preserves all signal details, sensitive to noise
- **Moving Average**: Good noise reduction, may blur sharp features
- **Exponential**: Adaptive smoothing, good for trending data

### Detector Module
- **Z-score**: Fast, assumes normal distribution, good for outliers
- **IQR**: Robust to distribution, good for extreme outliers
- **Change Point**: Detects regime changes, good for drift

### Filter Module
- **Identity**: No false positive reduction, preserves all detections
- **Temporal**: Reduces clustered alerts, may miss real clusters
- **Pattern Matching**: Learns from history, may over-suppress

## Requirements

- Python 3.7+
- NumPy
- Matplotlib (for visualization examples)

## Installation

Simply copy the following files to your project:
- `anomaly_detection_system.py` (main system)
- `test_anomaly_detection.py` (test suite)
- `example_usage.py` (examples and documentation)

## License

This anomaly detection system is provided as-is for educational and research purposes.

## Contributing

Feel free to extend the system with additional modules or detection algorithms following the established patterns.