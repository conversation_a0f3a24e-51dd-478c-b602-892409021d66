from typing import Dict
import numpy as np
import sys
sys.path.append("./EasyTSAD-bench")
from EasyTSAD.Controller import TSA<PERSON>ontroller
from opto.trace import node, bundle

import os
os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'
os.environ['TRACE_CUSTOMLLM_URL'] = "http://21.91.109.192:30000/v1"
os.environ['TRACE_CUSTOMLLM_MODEL'] = "deepseek-ai/DeepSeek-V3"
os.environ['TRACE_CUSTOMLLM_API_KEY'] = "sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt"

gctrl = TSADController()

gctrl.set_dataset(
    dataset_type="UTS",
    dirname="./datasets",
    datasets=["AIOPS", "WSD"],
)

from EasyTSAD.Methods import BaseMethod
from EasyTSAD.DataFactory import TSData

class AnomalyDetector(BaseMethod):
    def __init__(self, hparams) -> None:
        super().__init__()
        self.__anomaly_score = None
        self.name = "AnomalyDetector"
    
    def train_valid_phase(self, tsTrain: TSData):
        '''
        Define train and valid phase for naive mode. All time series needed are saved in tsTrain. 
        
        tsTrain's property :
            train (np.ndarray):
                The training set in numpy format;
            valid (np.ndarray):
                The validation set in numpy format;
            test (np.ndarray):
                The test set in numpy format;
            train_label (np.ndarray):
                The labels of training set in numpy format;
            test_label (np.ndarray):
                The labels of test set in numpy format;
            valid_label (np.ndarray):
                The labels of validation set in numpy format;
            info (dict):
                Some informations about the dataset, which might be useful.
                
        NOTE : test and test_label are not accessible in training phase
        '''
        ...
        
        return
            
    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):
        '''
        Define train and valid phase for all-in-one mode. All time series needed are saved in tsTrains. 
        
        tsTrain's structure:
            {
                "name of time series 1": tsData1,
                "name of time series 2": tsData2,
                ...
            }
            
        '''
        self
        ...
        
        return
        
    @bundle(trainable=True)
    def test_phase(self, tsData: TSData):
        '''
        Given a time series splitted into train/valid/test parts, generate anomaly scores for the test part.
        
        Args:
            tsData (TSData): The time series data.
            tsData's property that can be used:
                train (np.ndarray):
                    The training set in numpy format;
                valid (np.ndarray):
                    The validation set in numpy format;
                test (np.ndarray):
                    The test set in numpy format;
        
        return:
            None. You should save the anomaly scores in self.__anomaly_score.
            For each time series, the length of anomaly scores should be equal to the length of test set.   
            For a location that is likely to be an anomaly, the corressponding anomaly score should be larger than others.
        '''
        
        # For example
        test_len = tsData.test.shape[0]
        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])
        score = cat_data[1:] - cat_data[:-1]
        anomaly_score = score[-test_len:]
        self.__anomaly_score = anomaly_score
        
    def anomaly_score(self) -> np.ndarray:
        return self.__anomaly_score
    
    def param_statistic(self, save_file):
        param_info = "Your Algo. info"
        with open(save_file, 'w') as f:
            f.write(param_info)

def calculate_anomaly_score(test_data, train_data=[]):
    ```
    Calculate anomaly score for each point in the test data.
    
    Args:
        test_data (np.ndarray): The test data.
        train_data (np.ndarray): The training data.
        
    return:
        anomaly_score (np.ndarray): The anomaly score for each point in the test data.
    ```
    


import numpy as np
np.empty(0) 

np.array([])

"""============= Run CosAIService Algorithm ============="""
# Specifying methods and training schemas

training_schema = "naive"
method = "AnomalyDetector"  # string of our cos_ai_service algorithm class

# run models
gctrl.run_exps(
    method=method,
    training_schema=training_schema,
    cfg_path="YourAlgo.toml" # path/to/config
)
    
    
"""============= [EVALUATION SETTINGS] ============="""

from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA
# Specifying evaluation protocols
gctrl.set_evals(
    [
        PointF1PA(),
        EventF1PA(),
        EventF1PA(mode="squeeze")
    ]
)

gctrl.do_evals(
    method=method,
    training_schema=training_schema
)
    
    
"""============= [PLOTTING SETTINGS] ============="""

# plot anomaly scores for each curve
gctrl.plots(
    method=method,
    training_schema=training_schema
)