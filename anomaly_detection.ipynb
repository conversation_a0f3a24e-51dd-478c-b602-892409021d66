{"cells": [{"cell_type": "code", "execution_count": null, "id": "f4739609", "metadata": {}, "outputs": [], "source": ["from typing import Dict\n", "import numpy as np\n", "import sys\n", "sys.path.append(\"./EasyTSAD-bench\")\n", "from EasyTSAD.Controller import TSADController\n", "from opto.trace import node, bundle"]}, {"cell_type": "code", "execution_count": 2, "id": "7014eb4d", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'\n", "os.environ['TRACE_CUSTOMLLM_URL'] = \"http://21.91.109.192:30000/v1\"\n", "os.environ['TRACE_CUSTOMLLM_MODEL'] = \"deepseek-ai/DeepSeek-V3\"\n", "os.environ['TRACE_CUSTOMLLM_API_KEY'] = \"sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt\""]}, {"cell_type": "code", "execution_count": 3, "id": "2d8ba58b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-11 17:26:42,164) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-08-11 17:26:42,165) [INFO]: Dataset Directory has been loaded.\n"]}], "source": ["gctrl = TSADController()\n", "\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=\"./datasets\",\n", "    datasets=[\"AIOPS\", \"WSD\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e31f1b67", "metadata": {}, "outputs": [], "source": ["from EasyTSAD.Methods import BaseMethod\n", "from EasyTSAD.DataFactory import TSData\n", "\n", "class AnomalyDetector(BaseMethod):\n", "    def __init__(self, hparams) -> None:\n", "        super().__init__()\n", "        self.__anomaly_score = None\n", "        self.name = \"AnomalyDetector\"\n", "    \n", "    def train_valid_phase(self, tsTrain: TSData):\n", "        '''\n", "        Define train and valid phase for naive mode. All time series needed are saved in tsTrain. \n", "        \n", "        tsTrain's property :\n", "            train (np.ndarray):\n", "                The training set in numpy format;\n", "            valid (np.ndarray):\n", "                The validation set in numpy format;\n", "            test (np.ndarray):\n", "                The test set in numpy format;\n", "            train_label (np.ndarray):\n", "                The labels of training set in numpy format;\n", "            test_label (np.ndarray):\n", "                The labels of test set in numpy format;\n", "            valid_label (np.ndarray):\n", "                The labels of validation set in numpy format;\n", "            info (dict):\n", "                Some informations about the dataset, which might be useful.\n", "                \n", "        NOTE : test and test_label are not accessible in training phase\n", "        '''\n", "        ...\n", "        \n", "        return\n", "            \n", "    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):\n", "        '''\n", "        Define train and valid phase for all-in-one mode. All time series needed are saved in tsTrains. \n", "        \n", "        tsTrain's structure:\n", "            {\n", "                \"name of time series 1\": tsData1,\n", "                \"name of time series 2\": tsData2,\n", "                ...\n", "            }\n", "            \n", "        '''\n", "        self\n", "        ...\n", "        \n", "        return\n", "        \n", "    @bundle(trainable=True)\n", "    def test_phase(self, tsData: TSData):\n", "        '''\n", "        Given a time series splitted into train/valid/test parts, generate anomaly scores for the test part.\n", "        \n", "        Args:\n", "            tsData (TSData): The time series data.\n", "            tsData's property that can be used:\n", "                train (np.ndarray):\n", "                    The training set in numpy format;\n", "                valid (np.ndarray):\n", "                    The validation set in numpy format;\n", "                test (np.ndarray):\n", "                    The test set in numpy format;\n", "        \n", "        return:\n", "            None. You should save the anomaly scores in self.__anomaly_score.\n", "            For each time series, the length of anomaly scores should be equal to the length of test set.   \n", "            For a location that is likely to be an anomaly, the corressponding anomaly score should be larger than others.\n", "        '''\n", "        \n", "        # For example\n", "        test_len = tsData.test.shape[0]\n", "        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "        score = cat_data[1:] - cat_data[:-1]\n", "        anomaly_score = score[-test_len:]\n", "        self.__anomaly_score = anomaly_score\n", "        \n", "    def anomaly_score(self) -> np.ndarray:\n", "        return self.__anomaly_score\n", "    \n", "    def param_statistic(self, save_file):\n", "        param_info = \"Your Algo. info\"\n", "        with open(save_file, 'w') as f:\n", "            f.write(param_info)"]}, {"cell_type": "code", "execution_count": null, "id": "aacee840", "metadata": {}, "outputs": [], "source": ["def calculate_anomaly_score(test_data, train_data=[]):\n", "    ```\n", "    Calculate anomaly score for each point in the test data.\n", "    \n", "    Args:\n", "        test_data (np.ndarray): The test data.\n", "        train_data (np.ndarray): The training data.\n", "        \n", "    return:\n", "        anomaly_score (np.ndarray): The anomaly score for each point in the test data.\n", "    ```\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "d1075ed3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "cad29321", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-11 17:26:46,005) [INFO]: Run Experiments. Method[AnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-11 17:26:46,006) [INFO]: Use Customized Method Config. Path: /Users/<USER>/AlphaEvolve-for-Anomaly-Detector-Synthesis/YourAlgo.toml\n", "(2025-08-11 17:26:46,007) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD \n", "(2025-08-11 17:26:46,410) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 \n", "(2025-08-11 17:26:46,412) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 \n", "(2025-08-11 17:26:46,414) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n", "(2025-08-11 17:26:46,415) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 \n", "(2025-08-11 17:26:46,417) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 \n", "(2025-08-11 17:26:46,419) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c \n", "(2025-08-11 17:26:46,421) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 \n", "(2025-08-11 17:26:46,424) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 \n", "(2025-08-11 17:26:46,426) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa \n", "(2025-08-11 17:26:46,427) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 \n", "(2025-08-11 17:26:46,429) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 \n", "(2025-08-11 17:26:46,431) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd \n", "(2025-08-11 17:26:46,432) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd \n", "(2025-08-11 17:26:46,433) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 \n", "(2025-08-11 17:26:46,434) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd \n", "(2025-08-11 17:26:46,438) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 \n", "(2025-08-11 17:26:46,439) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 \n", "(2025-08-11 17:26:46,441) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d \n", "(2025-08-11 17:26:46,442) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 \n", "(2025-08-11 17:26:46,442) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da \n", "(2025-08-11 17:26:46,444) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea \n", "(2025-08-11 17:26:46,445) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa \n", "(2025-08-11 17:26:46,448) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 \n", "(2025-08-11 17:26:46,448) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 \n", "(2025-08-11 17:26:46,449) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 \n", "(2025-08-11 17:26:46,451) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d \n", "(2025-08-11 17:26:46,453) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 \n", "(2025-08-11 17:26:46,456) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af \n", "(2025-08-11 17:26:46,457) [INFO]:     [AnomalyDetector] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 \n", "(2025-08-11 17:26:46,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 135 \n", "(2025-08-11 17:26:46,459) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 61 \n", "(2025-08-11 17:26:46,460) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 95 \n", "(2025-08-11 17:26:46,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 132 \n", "(2025-08-11 17:26:46,461) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 59 \n", "(2025-08-11 17:26:46,462) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 92 \n", "(2025-08-11 17:26:46,463) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 66 \n", "(2025-08-11 17:26:46,464) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 104 \n", "(2025-08-11 17:26:46,464) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 50 \n", "(2025-08-11 17:26:46,466) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 68 \n", "(2025-08-11 17:26:46,468) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 103 \n", "(2025-08-11 17:26:46,469) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 57 \n", "(2025-08-11 17:26:46,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 168 \n", "(2025-08-11 17:26:46,471) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 157 \n", "(2025-08-11 17:26:46,472) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 150 \n", "(2025-08-11 17:26:46,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 159 \n", "(2025-08-11 17:26:46,473) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 32 \n", "(2025-08-11 17:26:46,474) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 166 \n", "(2025-08-11 17:26:46,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 192 \n", "(2025-08-11 17:26:46,475) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 35 \n", "(2025-08-11 17:26:46,476) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 195 \n", "(2025-08-11 17:26:46,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 161 \n", "(2025-08-11 17:26:46,477) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 102 \n", "(2025-08-11 17:26:46,478) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 69 \n", "(2025-08-11 17:26:46,479) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 56 \n", "(2025-08-11 17:26:46,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 105 \n", "(2025-08-11 17:26:46,480) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 51 \n", "(2025-08-11 17:26:46,481) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 58 \n", "(2025-08-11 17:26:46,482) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 133 \n", "(2025-08-11 17:26:46,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 67 \n", "(2025-08-11 17:26:46,483) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 93 \n", "(2025-08-11 17:26:46,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 134 \n", "(2025-08-11 17:26:46,484) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 94 \n", "(2025-08-11 17:26:46,485) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 60 \n", "(2025-08-11 17:26:46,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 34 \n", "(2025-08-11 17:26:46,486) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 160 \n", "(2025-08-11 17:26:46,487) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 194 \n", "(2025-08-11 17:26:46,489) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 33 \n", "(2025-08-11 17:26:46,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 158 \n", "(2025-08-11 17:26:46,490) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 193 \n", "(2025-08-11 17:26:46,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 167 \n", "(2025-08-11 17:26:46,491) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 151 \n", "(2025-08-11 17:26:46,492) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 169 \n", "(2025-08-11 17:26:46,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 156 \n", "(2025-08-11 17:26:46,493) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 204 \n", "(2025-08-11 17:26:46,494) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 203 \n", "(2025-08-11 17:26:46,495) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 202 \n", "(2025-08-11 17:26:46,496) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 205 \n", "(2025-08-11 17:26:46,497) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 174 \n", "(2025-08-11 17:26:46,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 180 \n", "(2025-08-11 17:26:46,498) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 20 \n", "(2025-08-11 17:26:46,499) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 187 \n", "(2025-08-11 17:26:46,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 18 \n", "(2025-08-11 17:26:46,500) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 173 \n", "(2025-08-11 17:26:46,501) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 27 \n", "(2025-08-11 17:26:46,502) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 9 \n", "(2025-08-11 17:26:46,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 0 \n", "(2025-08-11 17:26:46,503) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 145 \n", "(2025-08-11 17:26:46,504) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 11 \n", "(2025-08-11 17:26:46,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 142 \n", "(2025-08-11 17:26:46,505) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 7 \n", "(2025-08-11 17:26:46,506) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 29 \n", "(2025-08-11 17:26:46,507) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 16 \n", "(2025-08-11 17:26:46,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 189 \n", "(2025-08-11 17:26:46,508) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 129 \n", "(2025-08-11 17:26:46,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 42 \n", "(2025-08-11 17:26:46,509) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 89 \n", "(2025-08-11 17:26:46,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 116 \n", "(2025-08-11 17:26:46,512) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 45 \n", "(2025-08-11 17:26:46,513) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 111 \n", "(2025-08-11 17:26:46,514) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 73 \n", "(2025-08-11 17:26:46,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 118 \n", "(2025-08-11 17:26:46,515) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 87 \n", "(2025-08-11 17:26:46,516) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 127 \n", "(2025-08-11 17:26:46,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 80 \n", "(2025-08-11 17:26:46,517) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 74 \n", "(2025-08-11 17:26:46,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 120 \n", "(2025-08-11 17:26:46,518) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 6 \n", "(2025-08-11 17:26:46,519) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 28 \n", "(2025-08-11 17:26:46,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 143 \n", "(2025-08-11 17:26:46,520) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 17 \n", "(2025-08-11 17:26:46,521) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 188 \n", "(2025-08-11 17:26:46,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 144 \n", "(2025-08-11 17:26:46,522) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 1 \n", "(2025-08-11 17:26:46,523) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 10 \n", "(2025-08-11 17:26:46,524) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 172 \n", "(2025-08-11 17:26:46,526) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 186 \n", "(2025-08-11 17:26:46,528) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 19 \n", "(2025-08-11 17:26:46,529) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 26 \n", "(2025-08-11 17:26:46,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 8 \n", "(2025-08-11 17:26:46,530) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 181 \n", "(2025-08-11 17:26:46,532) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 175 \n", "(2025-08-11 17:26:46,533) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 21 \n", "(2025-08-11 17:26:46,535) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 75 \n", "(2025-08-11 17:26:46,537) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 81 \n", "(2025-08-11 17:26:46,538) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 121 \n", "(2025-08-11 17:26:46,539) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 119 \n", "(2025-08-11 17:26:46,540) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 86 \n", "(2025-08-11 17:26:46,541) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 72 \n", "(2025-08-11 17:26:46,542) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 126 \n", "(2025-08-11 17:26:46,543) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 44 \n", "(2025-08-11 17:26:46,544) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 110 \n", "(2025-08-11 17:26:46,545) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 43 \n", "(2025-08-11 17:26:46,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 128 \n", "(2025-08-11 17:26:46,546) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 88 \n", "(2025-08-11 17:26:46,547) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 117 \n", "(2025-08-11 17:26:46,548) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 198 \n", "(2025-08-11 17:26:46,549) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 153 \n", "(2025-08-11 17:26:46,550) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 38 \n", "(2025-08-11 17:26:46,551) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 154 \n", "(2025-08-11 17:26:46,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 36 \n", "(2025-08-11 17:26:46,552) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 196 \n", "(2025-08-11 17:26:46,553) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 162 \n", "(2025-08-11 17:26:46,554) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 31 \n", "(2025-08-11 17:26:46,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 165 \n", "(2025-08-11 17:26:46,555) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 191 \n", "(2025-08-11 17:26:46,556) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 131 \n", "(2025-08-11 17:26:46,557) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 91 \n", "(2025-08-11 17:26:46,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 65 \n", "(2025-08-11 17:26:46,558) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 136 \n", "(2025-08-11 17:26:46,559) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 62 \n", "(2025-08-11 17:26:46,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 96 \n", "(2025-08-11 17:26:46,560) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 109 \n", "(2025-08-11 17:26:46,561) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 100 \n", "(2025-08-11 17:26:46,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 54 \n", "(2025-08-11 17:26:46,562) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 107 \n", "(2025-08-11 17:26:46,563) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 98 \n", "(2025-08-11 17:26:46,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 138 \n", "(2025-08-11 17:26:46,564) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 53 \n", "(2025-08-11 17:26:46,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 30 \n", "(2025-08-11 17:26:46,565) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 190 \n", "(2025-08-11 17:26:46,566) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 164 \n", "(2025-08-11 17:26:46,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 37 \n", "(2025-08-11 17:26:46,567) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 163 \n", "(2025-08-11 17:26:46,568) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 197 \n", "(2025-08-11 17:26:46,569) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 155 \n", "(2025-08-11 17:26:46,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 199 \n", "(2025-08-11 17:26:46,570) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 39 \n", "(2025-08-11 17:26:46,571) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 152 \n", "(2025-08-11 17:26:46,572) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 106 \n", "(2025-08-11 17:26:46,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 99 \n", "(2025-08-11 17:26:46,573) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 52 \n", "(2025-08-11 17:26:46,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 139 \n", "(2025-08-11 17:26:46,574) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 101 \n", "(2025-08-11 17:26:46,575) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 55 \n", "(2025-08-11 17:26:46,575) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 137 \n", "(2025-08-11 17:26:46,576) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 97 \n", "(2025-08-11 17:26:46,577) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 108 \n", "(2025-08-11 17:26:46,577) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 63 \n", "(2025-08-11 17:26:46,578) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 130 \n", "(2025-08-11 17:26:46,579) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 64 \n", "(2025-08-11 17:26:46,581) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 90 \n", "(2025-08-11 17:26:46,581) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 209 \n", "(2025-08-11 17:26:46,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 200 \n", "(2025-08-11 17:26:46,582) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 207 \n", "(2025-08-11 17:26:46,583) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 206 \n", "(2025-08-11 17:26:46,584) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 201 \n", "(2025-08-11 17:26:46,584) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 208 \n", "(2025-08-11 17:26:46,585) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 46 \n", "(2025-08-11 17:26:46,586) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 79 \n", "(2025-08-11 17:26:46,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 112 \n", "(2025-08-11 17:26:46,587) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 41 \n", "(2025-08-11 17:26:46,588) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 115 \n", "(2025-08-11 17:26:46,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 83 \n", "(2025-08-11 17:26:46,589) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 77 \n", "(2025-08-11 17:26:46,590) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 123 \n", "(2025-08-11 17:26:46,591) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 48 \n", "(2025-08-11 17:26:46,591) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 70 \n", "(2025-08-11 17:26:46,592) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 84 \n", "(2025-08-11 17:26:46,592) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 124 \n", "(2025-08-11 17:26:46,593) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 184 \n", "(2025-08-11 17:26:46,594) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 170 \n", "(2025-08-11 17:26:46,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 24 \n", "(2025-08-11 17:26:46,595) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 177 \n", "(2025-08-11 17:26:46,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 183 \n", "(2025-08-11 17:26:46,596) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 148 \n", "(2025-08-11 17:26:46,597) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 23 \n", "(2025-08-11 17:26:46,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 141 \n", "(2025-08-11 17:26:46,598) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 4 \n", "(2025-08-11 17:26:46,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 15 \n", "(2025-08-11 17:26:46,599) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 3 \n", "(2025-08-11 17:26:46,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 146 \n", "(2025-08-11 17:26:46,600) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 12 \n", "(2025-08-11 17:26:46,601) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 179 \n", "(2025-08-11 17:26:46,602) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 85 \n", "(2025-08-11 17:26:46,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 71 \n", "(2025-08-11 17:26:46,603) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 125 \n", "(2025-08-11 17:26:46,604) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 76 \n", "(2025-08-11 17:26:46,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 82 \n", "(2025-08-11 17:26:46,605) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 49 \n", "(2025-08-11 17:26:46,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 122 \n", "(2025-08-11 17:26:46,606) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 40 \n", "(2025-08-11 17:26:46,607) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 114 \n", "(2025-08-11 17:26:46,607) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 47 \n", "(2025-08-11 17:26:46,608) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 113 \n", "(2025-08-11 17:26:46,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 78 \n", "(2025-08-11 17:26:46,609) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 147 \n", "(2025-08-11 17:26:46,610) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 2 \n", "(2025-08-11 17:26:46,611) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 178 \n", "(2025-08-11 17:26:46,611) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 13 \n", "(2025-08-11 17:26:46,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 5 \n", "(2025-08-11 17:26:46,612) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 140 \n", "(2025-08-11 17:26:46,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 14 \n", "(2025-08-11 17:26:46,613) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 182 \n", "(2025-08-11 17:26:46,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 176 \n", "(2025-08-11 17:26:46,614) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 22 \n", "(2025-08-11 17:26:46,615) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 149 \n", "(2025-08-11 17:26:46,616) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 171 \n", "(2025-08-11 17:26:46,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 185 \n", "(2025-08-11 17:26:46,617) [INFO]:     [AnomalyDetector] handling dataset WSD | curve 25 \n", "(2025-08-11 17:26:47,813) [INFO]: Register evaluations\n", "(2025-08-11 17:26:47,814) [INFO]: Perform evaluations. Method[AnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-11 17:26:47,814) [INFO]:     [Load Data (All)] DataSets: AIOP<PERSON>,WSD \n", "(2025-08-11 17:26:47,880) [INFO]:     [AnomalyDetector] Eval dataset AIOPS <<<\n", "(2025-08-11 17:26:47,880) [INFO]:         [AIOPS] Using margins (0, 5)\n", "(2025-08-11 17:26:54,045) [INFO]:     [AnomalyDetector] Eval dataset WSD <<<\n", "(2025-08-11 17:26:54,046) [INFO]:         [WSD] Using margins (0, 3)\n", "(2025-08-11 17:26:54,145) [WARNING]: [AnomalyDetector] WSD: 20     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:54,678) [WARNING]: [AnomalyDetector] WSD: 127     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:54,815) [WARNING]: [AnomalyDetector] WSD: 126     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:54,920) [WARNING]: [AnomalyDetector] WSD: 130     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,025) [WARNING]: [AnomalyDetector] WSD: 18     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,059) [WARNING]: [AnomalyDetector] WSD: 24     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,168) [WARNING]: [AnomalyDetector] WSD: 208     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,352) [WARNING]: [AnomalyDetector] WSD: 195     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,352) [WARNING]: [AnomalyDetector] WSD: 43     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,457) [WARNING]: [AnomalyDetector] WSD: 56     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,458) [WARNING]: [AnomalyDetector] WSD: 42     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,569) [WARNING]: [AnomalyDetector] WSD: 81     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,711) [WARNING]: [AnomalyDetector] WSD: 141     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,962) [WARNING]: [AnomalyDetector] WSD: 6     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,962) [WARNING]: [AnomalyDetector] WSD: 40     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,963) [WARNING]: [AnomalyDetector] WSD: 41     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,963) [WARNING]: [AnomalyDetector] WSD: 55     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:55,964) [WARNING]: [AnomalyDetector] WSD: 7     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,074) [WARNING]: [AnomalyDetector] WSD: 82     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,215) [WARNING]: [AnomalyDetector] WSD: 92     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,360) [WARNING]: [AnomalyDetector] WSD: 3     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,361) [WARNING]: [AnomalyDetector] WSD: 51     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,622) [WARNING]: [AnomalyDetector] WSD: 44     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,622) [WARNING]: [AnomalyDetector] WSD: 2     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,731) [WARNING]: [AnomalyDetector] WSD: 87     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,842) [WARNING]: [AnomalyDetector] WSD: 91     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,943) [WARNING]: [AnomalyDetector] WSD: 52     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:56,944) [WARNING]: [AnomalyDetector] WSD: 0     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,129) [WARNING]: [AnomalyDetector] WSD: 1     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,233) [WARNING]: [AnomalyDetector] WSD: 90     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,498) [WARNING]: [AnomalyDetector] WSD: 76     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,499) [WARNING]: [AnomalyDetector] WSD: 189     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,499) [WARNING]: [AnomalyDetector] WSD: 77     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,500) [WARNING]: [AnomalyDetector] WSD: 63     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,538) [WARNING]: [AnomalyDetector] WSD: 88     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,539) [WARNING]: [AnomalyDetector] WSD: 162     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,761) [WARNING]: [AnomalyDetector] WSD: 75     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:57,976) [WARNING]: [AnomalyDetector] WSD: 175     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:58,207) [WARNING]: [AnomalyDetector] WSD: 64     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:58,467) [WARNING]: [AnomalyDetector] WSD: 206     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:58,773) [WARNING]: [AnomalyDetector] WSD: 8     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,044) [WARNING]: [AnomalyDetector] WSD: 100     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,045) [WARNING]: [AnomalyDetector] WSD: 114     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,227) [WARNING]: [AnomalyDetector] WSD: 129     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,228) [WARNING]: [AnomalyDetector] WSD: 115     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,714) [WARNING]: [AnomalyDetector] WSD: 139     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,865) [WARNING]: [AnomalyDetector] WSD: 39     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,866) [WARNING]: [AnomalyDetector] WSD: 11     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,943) [WARNING]: [AnomalyDetector] WSD: 138     All test labels are normal. SKIP this curve. <<<\n", "(2025-08-11 17:26:59,953) [INFO]: Plotting. Method[AnomalyDetector], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-11 17:26:59,953) [INFO]:     [Load Data (All)] DataSets: AIOPS,WSD \n", "(2025-08-11 17:27:00,012) [INFO]:     [AnomalyDetector] Plot dataset AIOPS score only \n", "(2025-08-11 17:27:09,230) [INFO]:     [AnomalyDetector] Plot dataset WSD score only \n"]}], "source": ["\"\"\"============= Run CosAIService Algorithm =============\"\"\"\n", "# Specifying methods and training schemas\n", "\n", "training_schema = \"naive\"\n", "method = \"AnomalyDetector\"  # string of our cos_ai_service algorithm class\n", "\n", "# run models\n", "gctrl.run_exps(\n", "    method=method,\n", "    training_schema=training_schema,\n", "    cfg_path=\"YourAlgo.toml\" # path/to/config\n", ")\n", "    \n", "    \n", "\"\"\"============= [EVALUATION SETTINGS] =============\"\"\"\n", "\n", "from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA\n", "# Specifying evaluation protocols\n", "gctrl.set_evals(\n", "    [\n", "        PointF1PA(),\n", "        EventF1PA(),\n", "        EventF1PA(mode=\"squeeze\")\n", "    ]\n", ")\n", "\n", "gctrl.do_evals(\n", "    method=method,\n", "    training_schema=training_schema\n", ")\n", "    \n", "    \n", "\"\"\"============= [PLOTTING SETTINGS] =============\"\"\"\n", "\n", "# plot anomaly scores for each curve\n", "gctrl.plots(\n", "    method=method,\n", "    training_schema=training_schema\n", ")"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}