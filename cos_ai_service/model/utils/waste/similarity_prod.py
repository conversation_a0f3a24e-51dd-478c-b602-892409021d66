import time

from dtaidistance import dtw


# 生产环境的代码，实际上可视化效果不好，只能看见最后一个点的dtw数值

def analysis_DTW(data):
    """分析DTW"""

    param_list = data["param"]["param_list"]
    time_window = int(param_list["time_window_dtw"])
    time_window_focus = int(param_list["time_window_focus"])
    metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], \
        data['smooth']['ewma'][
            "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
    if "DTW" not in data["log"]:
        data["log"]["DTW"] = [0] * len(metric_today)

    ini_focus = max(0, len(metric_today) - time_window_focus)
    start = max(0, len(metric_today) - time_window) if time_window != -1 else len(
        metric_today) - ini_focus

    slice_yesterday, slice_week = dtw.distance(metric_today[start:], metric_yesterday[start:]), dtw.distance(
        metric_today[start:], metric_lastweek[start:])

    slice = min(slice_yesterday, slice_week)
    #print('analysis_DTW slice:', slice)
    thre = param_list["param_dtw"]
    #print('analysis_DTW thre:', thre)
    if slice < thre:
        data["is_change_err"][-1] = 0
    else:
        data["log"]["DTW"][-1] = slice
    data["curve"]["low"]["DTW"] = slice
    #print('analysis_DTW data["is_change_err"][-1]:', data["is_change_err"][-1])
    return data


def analysis_D2(data):
    dtw_yes, dtw_week = dtw.distance(data['downsample']["metric_today"], data['downsample']["metric_yesterday"]) \
        , dtw.distance(data['downsample']["metric_today"], data['downsample']["metric_lastweek"])
    dtw_all = min(dtw_yes, dtw_week)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    need_alarm = "模糊区间"
    #print('analysis_D2 dtw_all:', dtw_all)
    #print('analysis_D2 thre_low:', thre_low)
    #print('analysis_D2 thre_high:', thre_high)
    if dtw_all < thre_low:
        data["is_change_err"][-1] = 0
        need_alarm = "不告警"
    if dtw_all > thre_high:
        data["is_change_err"][-1] = 1
        need_alarm = "强制告警"
    data["log"]["DTW_all"] = f"兼顾时效，差异度{dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]，{need_alarm}"
    #print('analysis_DTW data["is_change_err"][-1]:', data["is_change_err"][-1])
    return data


def analysis_D3(data):
    metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
        "metric_yesterday"], data['downsample']["metric_lastweek"]
    data["is_change_err"] = [0] * len(data["is_change_err"])
    # 计算最近的相似度是否发生变化
    time_window = 300
    time_window = int(time_window / 4320 * data["param"]["param_list"]["time_window_dtw"])  # 注意降采样了
    l_to, l_yes, l_last = data['downsample']["metric_today"][-time_window:], data['downsample'][
                                                                                 "metric_yesterday"][-time_window:], \
        data['downsample']["metric_lastweek"][-time_window:]

    dtw_all_yes, dtw_all_last, dtw_yes, dtw_last = \
        dtw.distance(metric_today, metric_yesterday), \
            dtw.distance(metric_today, metric_lastweek), \
            dtw.distance(l_to, l_yes), \
            dtw.distance(l_to, l_last)

    dtw_all = min(dtw_all_yes, dtw_all_last)
    dtw_l = min(dtw_yes, dtw_last)
    print("dtw_最近:", dtw_l)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    if dtw_l > thre_low and dtw_all > thre_high:  # 若最近的差异度高，并且差异度不是周期偏移导致的（总体），告警
        data["is_change_err"][-1] = 1

    data["log"]["DTW_all"] = f"仅关注相似，{dtw_l:.3f}, {dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]"
    return data
