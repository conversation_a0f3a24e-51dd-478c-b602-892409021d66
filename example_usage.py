"""
Example Usage and Documentation for Online Anomaly Detection System

This file demonstrates how to use the modular online anomaly detection system
with different configurations. The system processes data point-by-point in
streaming fashion, ensuring no future data leakage for real-time applications.

Author: Anomaly Detection System Examples
Date: 2025-08-12
"""

import numpy as np
import matplotlib.pyplot as plt
from anomaly_detection_system import detect_anomalies, create_online_detector


def basic_usage_example():
    """Demonstrate basic usage with default settings."""
    print("="*60)
    print("BASIC USAGE EXAMPLE - ONLINE PROCESSING")
    print("="*60)

    # Generate sample time series data
    np.random.seed(42)
    data = np.random.normal(0, 1, 100)

    # Add some clear anomalies
    data[25] = 5.0   # Spike
    data[50] = -4.0  # Negative spike
    data[75] = 3.5   # Another spike

    print("Method 1: Batch processing (simulates online internally)")
    scores, flags = detect_anomalies(data)
    print(f"  Data length: {len(data)}")
    print(f"  Anomalies detected: {np.sum(flags)}")
    print(f"  Anomaly positions: {np.where(flags)[0]}")
    print(f"  Max anomaly score: {np.max(scores):.3f}")

    print("\nMethod 2: True streaming processing")
    detector = create_online_detector()
    streaming_scores = []
    streaming_flags = []

    for i, value in enumerate(data):
        score, flag = detector.process_point(value)
        streaming_scores.append(score)
        streaming_flags.append(flag)

        if flag:
            print(f"  Time {i:2d}: Anomaly detected! Value={value:5.2f}, Score={score:.3f}")

    streaming_scores = np.array(streaming_scores)
    streaming_flags = np.array(streaming_flags)

    print(f"\nStreaming results: {np.sum(streaming_flags)} anomalies detected")
    print(f"Results match: {np.array_equal(flags, streaming_flags)}")

    return data, scores, flags


def sampler_module_examples():
    """Demonstrate different sampler module configurations."""
    print("\n" + "="*60)
    print("SAMPLER MODULE EXAMPLES")
    print("="*60)
    
    # Create large dataset for sampling demonstration
    np.random.seed(42)
    large_data = np.random.normal(0, 1, 1000)
    large_data[250] = 5.0  # Add anomaly
    large_data[500] = -4.0
    large_data[750] = 3.5
    
    # Example 1: No sampling (identity)
    config1 = {"sampler": {"method": "identity"}}
    scores1, flags1 = detect_anomalies(large_data, config1)
    print(f"Identity sampling - Processing time: Fast, Anomalies: {np.sum(flags1)}")
    
    # Example 2: Uniform downsampling
    config2 = {"sampler": {"method": "uniform", "step_size": 3}}
    scores2, flags2 = detect_anomalies(large_data, config2)
    print(f"Uniform sampling (step=3) - Processing time: Faster, Anomalies: {np.sum(flags2)}")
    
    # Example 3: Adaptive sampling
    config3 = {"sampler": {"method": "adaptive", "window_size": 20, "variance_threshold": 0.5}}
    scores3, flags3 = detect_anomalies(large_data, config3)
    print(f"Adaptive sampling - Processing time: Medium, Anomalies: {np.sum(flags3)}")
    
    print("\nSampler Trade-offs:")
    print("- Identity: Highest accuracy, slowest for large data")
    print("- Uniform: Fastest, may miss short-duration anomalies")
    print("- Adaptive: Good balance, preserves high-variance regions")


def denoiser_module_examples():
    """Demonstrate different denoiser module configurations."""
    print("\n" + "="*60)
    print("DENOISER MODULE EXAMPLES")
    print("="*60)
    
    # Create noisy data with anomalies
    np.random.seed(42)
    clean_signal = np.sin(np.linspace(0, 4*np.pi, 200))
    noise = np.random.normal(0, 0.3, 200)
    noisy_data = clean_signal + noise
    
    # Add anomalies
    noisy_data[50] = 3.0
    noisy_data[100] = -2.5
    noisy_data[150] = 2.8
    
    # Example 1: No denoising
    config1 = {"denoiser": {"method": "identity"}}
    scores1, flags1 = detect_anomalies(noisy_data, config1)
    print(f"No denoising - Anomalies detected: {np.sum(flags1)} (may include noise)")
    
    # Example 2: Moving average
    config2 = {"denoiser": {"method": "moving_average", "window_size": 7}}
    scores2, flags2 = detect_anomalies(noisy_data, config2)
    print(f"Moving average (window=7) - Anomalies detected: {np.sum(flags2)}")
    
    # Example 3: Exponential smoothing
    config3 = {"denoiser": {"method": "exponential", "alpha": 0.2}}
    scores3, flags3 = detect_anomalies(noisy_data, config3)
    print(f"Exponential smoothing (α=0.2) - Anomalies detected: {np.sum(flags3)}")
    
    print("\nDenoiser Trade-offs:")
    print("- Identity: Preserves all signal details, sensitive to noise")
    print("- Moving average: Good noise reduction, may blur sharp anomalies")
    print("- Exponential: Adaptive smoothing, good for trending data")


def detector_module_examples():
    """Demonstrate different detector module configurations."""
    print("\n" + "="*60)
    print("DETECTOR MODULE EXAMPLES")
    print("="*60)
    
    # Create different types of anomalous data
    np.random.seed(42)
    
    # Data with outliers
    outlier_data = np.random.normal(0, 1, 200)
    outlier_data[50] = 5.0
    outlier_data[100] = -4.0
    outlier_data[150] = 3.5
    
    # Data with change point
    change_data = np.concatenate([
        np.random.normal(0, 0.5, 100),
        np.random.normal(3, 0.5, 100)
    ])
    
    # Example 1: Z-score detection
    config1 = {"detector": {"method": "zscore", "threshold": 2.5}}
    scores1, flags1 = detect_anomalies(outlier_data, config1)
    print(f"Z-score (threshold=2.5) - Outlier anomalies: {np.sum(flags1)}")
    
    # Example 2: IQR detection
    config2 = {"detector": {"method": "iqr", "multiplier": 1.5}}
    scores2, flags2 = detect_anomalies(outlier_data, config2)
    print(f"IQR (multiplier=1.5) - Outlier anomalies: {np.sum(flags2)}")
    
    # Example 3: Change point detection
    config3 = {"detector": {"method": "changepoint", "window_size": 15, "threshold": 2.0}}
    scores3, flags3 = detect_anomalies(change_data, config3)
    print(f"Change point detection - Change anomalies: {np.sum(flags3)}")
    
    print("\nDetector Trade-offs:")
    print("- Z-score: Good for outliers, assumes normal distribution")
    print("- IQR: Robust to distribution, good for extreme outliers")
    print("- Change point: Detects regime changes, good for drift detection")


def filter_module_examples():
    """Demonstrate different filter module configurations."""
    print("\n" + "="*60)
    print("FILTER MODULE EXAMPLES")
    print("="*60)
    
    # Create data with clustered anomalies
    np.random.seed(42)
    data = np.random.normal(0, 1, 200)
    
    # Add clustered anomalies
    cluster_positions = [48, 49, 50, 51, 52]  # Tight cluster
    for pos in cluster_positions:
        data[pos] = 4.0
    
    data[100] = 3.5  # Isolated anomaly
    data[150] = -3.0  # Another isolated anomaly
    
    # Example 1: No filtering
    config1 = {"filter": {"method": "identity"}}
    scores1, flags1 = detect_anomalies(data, config1)
    print(f"No filtering - Total anomalies: {np.sum(flags1)}")
    
    # Example 2: Temporal filtering
    config2 = {"filter": {"method": "temporal", "min_distance": 5}}
    scores2, flags2 = detect_anomalies(data, config2)
    print(f"Temporal filtering (min_distance=5) - Filtered anomalies: {np.sum(flags2)}")
    
    # Example 3: Pattern matching (run twice to see effect)
    config3 = {"filter": {"method": "pattern_matching", "pattern_window": 10, "similarity_threshold": 0.7}}
    scores3a, flags3a = detect_anomalies(data, config3)
    scores3b, flags3b = detect_anomalies(data, config3)  # Second run should filter more
    print(f"Pattern matching - First run: {np.sum(flags3a)}, Second run: {np.sum(flags3b)}")
    
    print("\nFilter Trade-offs:")
    print("- Identity: No false positive reduction, may have noise")
    print("- Temporal: Reduces clustered false positives, may miss real clusters")
    print("- Pattern matching: Learns from history, may over-suppress recurring patterns")


def online_processing_demonstration():
    """Demonstrate the online processing capabilities."""
    print("\n" + "="*60)
    print("ONLINE PROCESSING DEMONSTRATION")
    print("="*60)

    print("This demonstrates how the system processes data point-by-point")
    print("without access to future data, suitable for real-time applications.\n")

    # Create streaming data
    np.random.seed(123)

    # Simulate real-time data stream
    detector = create_online_detector({
        "detector": {"method": "zscore", "threshold": 2.5, "min_samples": 5},
        "filter": {"method": "temporal", "min_distance": 3}
    })

    print("Simulating real-time data stream:")
    print("Time | Value | Score | Anomaly | Historical Data Size")
    print("-" * 55)

    # Generate and process data points one by one
    for t in range(20):
        # Simulate different types of data
        if t < 10:
            value = np.random.normal(0, 1)  # Normal data
        elif t == 10:
            value = 5.0  # Clear anomaly
        elif t < 15:
            value = np.random.normal(0, 1)  # More normal data
        elif t == 15:
            value = -4.0  # Another anomaly
        else:
            value = np.random.normal(0, 1)  # Normal data

        # Process the point (only has access to previous data)
        score, is_anomaly = detector.process_point(value)

        # Show the processing step
        status = "YES" if is_anomaly else "no"
        hist_size = len(detector.historical_data) - 1  # Exclude current point
        print(f"{t:4d} | {value:5.2f} | {score:5.3f} | {status:7s} | {hist_size:17d}")

    print(f"\nTotal anomalies detected: {sum(1 for t in range(20) if detector.process_point(np.random.normal(0,1))[1])}")
    print("Note: Each time step only used data from previous time steps.")


def real_world_scenarios():
    """Demonstrate configurations for different real-world scenarios."""
    print("\n" + "="*60)
    print("REAL-WORLD SCENARIO CONFIGURATIONS")
    print("="*60)
    
    # Scenario 1: High-frequency sensor data with noise
    print("\n1. High-frequency sensor data (e.g., IoT sensors)")
    config_sensor = {
        "sampler": {"method": "uniform", "step_size": 5},  # Reduce computational load
        "denoiser": {"method": "moving_average", "window_size": 10},  # Smooth noise
        "detector": {"method": "zscore", "threshold": 3.0},  # Conservative threshold
        "filter": {"method": "temporal", "min_distance": 20}  # Avoid alarm fatigue
    }
    print("Configuration: Uniform sampling + Moving average + Z-score + Temporal filter")
    print("Benefits: Fast processing, noise reduction, fewer false alarms")
    
    # Scenario 2: Financial time series
    print("\n2. Financial time series (e.g., stock prices)")
    config_finance = {
        "sampler": {"method": "adaptive", "window_size": 20, "variance_threshold": 0.1},
        "denoiser": {"method": "exponential", "alpha": 0.1},  # Preserve trends
        "detector": {"method": "iqr", "multiplier": 2.0},  # Robust to outliers
        "filter": {"method": "pattern_matching", "pattern_window": 15}  # Learn market patterns
    }
    print("Configuration: Adaptive sampling + Exponential smoothing + IQR + Pattern matching")
    print("Benefits: Preserves volatility patterns, robust detection, learns market behavior")
    
    # Scenario 3: System monitoring (e.g., CPU usage)
    print("\n3. System monitoring (e.g., server metrics)")
    config_system = {
        "sampler": {"method": "identity"},  # Keep all data points
        "denoiser": {"method": "moving_average", "window_size": 5},  # Light smoothing
        "detector": {"method": "changepoint", "window_size": 30, "threshold": 1.5},
        "filter": {"method": "temporal", "min_distance": 10}  # Avoid alert spam
    }
    print("Configuration: No sampling + Light smoothing + Change point + Temporal filter")
    print("Benefits: Detects performance degradation, reduces alert fatigue")
    
    # Scenario 4: Quality control in manufacturing
    print("\n4. Manufacturing quality control")
    config_quality = {
        "sampler": {"method": "identity"},  # Every measurement matters
        "denoiser": {"method": "identity"},  # Preserve measurement precision
        "detector": {"method": "zscore", "threshold": 2.0, "window_size": 50},  # Rolling statistics
        "filter": {"method": "identity"}  # Don't filter quality issues
    }
    print("Configuration: No sampling + No denoising + Rolling Z-score + No filtering")
    print("Benefits: Maximum sensitivity, preserves all quality information")


def performance_optimization_guide():
    """Provide guidance on optimizing performance for different use cases."""
    print("\n" + "="*60)
    print("PERFORMANCE OPTIMIZATION GUIDE")
    print("="*60)
    
    print("\nFor SPEED optimization:")
    print("- Use uniform sampling with larger step sizes")
    print("- Use identity denoising (skip smoothing)")
    print("- Use simple detectors (zscore with global statistics)")
    print("- Use identity filtering (skip post-processing)")
    
    print("\nFor ACCURACY optimization:")
    print("- Use identity sampling (keep all data)")
    print("- Use appropriate denoising for your data type")
    print("- Use detector that matches your anomaly type")
    print("- Use filtering to reduce false positives")
    
    print("\nFor MEMORY efficiency:")
    print("- Use aggressive uniform sampling")
    print("- Limit pattern matching filter history")
    print("- Process data in chunks for very large datasets")
    
    print("\nFor REAL-TIME applications:")
    print("- Use adaptive sampling to balance speed/accuracy")
    print("- Use exponential smoothing (single-pass)")
    print("- Use rolling window statistics")
    print("- Use temporal filtering with small min_distance")


def create_visualization_example():
    """Create a comprehensive visualization of the detection pipeline."""
    print("\n" + "="*60)
    print("VISUALIZATION EXAMPLE")
    print("="*60)
    
    # Generate complex test data
    np.random.seed(42)
    t = np.linspace(0, 10, 500)
    signal = np.sin(t) + 0.5 * np.sin(3*t)  # Base signal
    noise = np.random.normal(0, 0.2, len(t))  # Noise
    data = signal + noise
    
    # Add different types of anomalies
    data[100] = 3.0    # Spike
    data[200:210] = 2.5  # Plateau anomaly
    data[300] = -2.5   # Negative spike
    data[400:450] += np.linspace(0, 1.5, 50)  # Drift
    
    # Test different configurations
    configs = {
        'Basic': {},
        'Denoised': {"denoiser": {"method": "moving_average", "window_size": 10}},
        'Robust': {"detector": {"method": "iqr", "multiplier": 1.5}},
        'Filtered': {
            "denoiser": {"method": "moving_average", "window_size": 5},
            "detector": {"method": "zscore", "threshold": 2.5},
            "filter": {"method": "temporal", "min_distance": 10}
        }
    }
    
    plt.figure(figsize=(15, 12))
    
    for i, (name, config) in enumerate(configs.items()):
        scores, flags = detect_anomalies(data, config)
        
        plt.subplot(2, 2, i+1)
        plt.plot(t, data, 'b-', alpha=0.7, label='Data')
        plt.plot(t, scores * np.max(data), 'g-', alpha=0.8, label='Anomaly Scores')
        plt.scatter(t[flags], data[flags], color='red', s=30, label='Detected Anomalies', zorder=5)
        plt.title(f'{name} Configuration\nDetected: {np.sum(flags)} anomalies')
        plt.xlabel('Time')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("Visualization shows how different configurations affect detection results.")


if __name__ == "__main__":
    # Run all examples
    basic_usage_example()
    online_processing_demonstration()
    sampler_module_examples()
    denoiser_module_examples()
    detector_module_examples()
    filter_module_examples()
    real_world_scenarios()
    performance_optimization_guide()

    # Create visualization
    try:
        create_visualization_example()
    except ImportError:
        print("\nNote: matplotlib not available for visualization example")

    print("\n" + "="*60)
    print("ONLINE ANOMALY DETECTION EXAMPLES COMPLETE")
    print("="*60)
    print("The system processes data point-by-point without future data access.")
    print("Perfect for real-time anomaly detection in streaming applications.")
    print("Modify configurations based on your specific use case and requirements.")


# Additional utility functions for advanced usage

def batch_processing_example():
    """Example of processing multiple time series efficiently."""
    print("\n" + "="*60)
    print("BATCH PROCESSING EXAMPLE")
    print("="*60)

    # Simulate multiple time series
    np.random.seed(42)
    time_series_list = []
    for i in range(5):
        data = np.random.normal(0, 1, 200)
        data[50 + i*10] = 4.0  # Add anomaly at different positions
        time_series_list.append(data)

    # Process each series with the same configuration
    config = {
        "denoiser": {"method": "moving_average", "window_size": 5},
        "detector": {"method": "zscore", "threshold": 2.5},
        "filter": {"method": "temporal", "min_distance": 5}
    }

    results = []
    for i, data in enumerate(time_series_list):
        scores, flags = detect_anomalies(data, config)
        anomaly_count = np.sum(flags)
        results.append((i, anomaly_count, np.where(flags)[0]))
        print(f"Series {i}: {anomaly_count} anomalies at positions {np.where(flags)[0]}")

    return results


def custom_configuration_builder():
    """Helper function to build configurations for common scenarios."""

    def build_config(scenario="general", sensitivity="medium", speed="medium"):
        """
        Build configuration for common scenarios.

        Args:
            scenario: "sensor", "finance", "system", "quality", "general"
            sensitivity: "low", "medium", "high"
            speed: "slow", "medium", "fast"
        """
        config = {}

        # Speed settings
        if speed == "fast":
            config["sampler"] = {"method": "uniform", "step_size": 3}
        elif speed == "medium":
            config["sampler"] = {"method": "adaptive", "window_size": 15}
        else:  # slow
            config["sampler"] = {"method": "identity"}

        # Scenario-specific settings
        if scenario == "sensor":
            config["denoiser"] = {"method": "moving_average", "window_size": 8}
            config["detector"] = {"method": "zscore", "threshold": 3.0}
            config["filter"] = {"method": "temporal", "min_distance": 15}

        elif scenario == "finance":
            config["denoiser"] = {"method": "exponential", "alpha": 0.1}
            config["detector"] = {"method": "iqr", "multiplier": 2.0}
            config["filter"] = {"method": "pattern_matching", "pattern_window": 20}

        elif scenario == "system":
            config["denoiser"] = {"method": "moving_average", "window_size": 5}
            config["detector"] = {"method": "changepoint", "window_size": 25}
            config["filter"] = {"method": "temporal", "min_distance": 10}

        elif scenario == "quality":
            config["denoiser"] = {"method": "identity"}
            config["detector"] = {"method": "zscore", "window_size": 30}
            config["filter"] = {"method": "identity"}

        # Sensitivity adjustments
        if "detector" in config:
            if sensitivity == "high":
                if config["detector"]["method"] == "zscore":
                    config["detector"]["threshold"] = 2.0
                elif config["detector"]["method"] == "iqr":
                    config["detector"]["multiplier"] = 1.2
                elif config["detector"]["method"] == "changepoint":
                    config["detector"]["threshold"] = 1.5
            elif sensitivity == "low":
                if config["detector"]["method"] == "zscore":
                    config["detector"]["threshold"] = 3.5
                elif config["detector"]["method"] == "iqr":
                    config["detector"]["multiplier"] = 2.5
                elif config["detector"]["method"] == "changepoint":
                    config["detector"]["threshold"] = 3.0

        return config

    # Example usage
    print("\nConfiguration Builder Examples:")

    config1 = build_config("sensor", "medium", "fast")
    print(f"Sensor config (medium sensitivity, fast): {config1}")

    config2 = build_config("finance", "high", "medium")
    print(f"Finance config (high sensitivity, medium speed): {config2}")

    config3 = build_config("quality", "high", "slow")
    print(f"Quality config (high sensitivity, slow): {config3}")

    return build_config
