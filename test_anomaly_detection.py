"""
Comprehensive Test Suite for Online Anomaly Detection System

This module contains extensive test cases covering:
- Online processing validation (no future data leakage)
- Basic functionality with default settings
- Each module option individually and in combination
- Edge cases (empty data, single point, constant values, etc.)
- Performance comparison between different module configurations
- Synthetic test data with known anomalies to validate detection accuracy
- Real-world-like test scenarios with different anomaly types
- Streaming vs batch processing consistency

Author: Anomaly Detection System Test Suite
Date: 2025-08-12
"""

import numpy as np
import time
import unittest
from typing import List, Tuple
import matplotlib.pyplot as plt
from anomaly_detection_system import (
    detect_anomalies, create_online_detector, OnlineAnomalyDetector,
    SamplerModule, DenoiserModule, DetectorModule, FilterModule
)


class TestAnomalyDetectionSystem(unittest.TestCase):
    """Comprehensive test suite for the anomaly detection system."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Generate synthetic test data
        np.random.seed(42)
        self.normal_data = np.random.normal(0, 1, 100)
        self.constant_data = np.ones(50)
        self.empty_data = np.array([])
        self.single_point = np.array([5.0])
        
        # Create data with known anomalies
        self.data_with_spikes = self._create_spike_data()
        self.data_with_drift = self._create_drift_data()
        self.data_with_outliers = self._create_outlier_data()
        
    def _create_spike_data(self) -> np.ndarray:
        """Create time series with spike anomalies."""
        data = np.random.normal(0, 0.5, 200)
        # Add spikes at known positions
        spike_positions = [50, 100, 150]
        for pos in spike_positions:
            data[pos] = 5.0  # Clear spike
        return data
    
    def _create_drift_data(self) -> np.ndarray:
        """Create time series with drift anomalies."""
        data = np.random.normal(0, 0.5, 200)
        # Add gradual drift
        drift_start = 100
        for i in range(drift_start, len(data)):
            data[i] += (i - drift_start) * 0.05
        return data
    
    def _create_outlier_data(self) -> np.ndarray:
        """Create time series with outlier anomalies."""
        data = np.random.normal(0, 1, 200)
        # Add outliers at known positions
        outlier_positions = [25, 75, 125, 175]
        for pos in outlier_positions:
            data[pos] = np.random.choice([-4, 4])  # Clear outliers
        return data
    
    def test_basic_functionality_default_settings(self):
        """Test basic functionality with default settings."""
        scores, flags = detect_anomalies(self.normal_data)
        
        # Check output types and shapes
        self.assertIsInstance(scores, np.ndarray)
        self.assertIsInstance(flags, np.ndarray)
        self.assertEqual(len(scores), len(self.normal_data))
        self.assertEqual(len(flags), len(self.normal_data))
        self.assertEqual(flags.dtype, bool)
        
        # Check score range
        self.assertTrue(np.all(scores >= 0))
        self.assertTrue(np.all(scores <= 1))
    
    def test_edge_cases(self):
        """Test edge cases: empty data, single point, constant values."""
        # Empty data
        scores, flags = detect_anomalies(self.empty_data)
        self.assertEqual(len(scores), 0)
        self.assertEqual(len(flags), 0)
        
        # Single point
        scores, flags = detect_anomalies(self.single_point)
        self.assertEqual(len(scores), 1)
        self.assertEqual(len(flags), 1)
        self.assertEqual(scores[0], 0.0)
        self.assertEqual(flags[0], False)
        
        # Constant data
        scores, flags = detect_anomalies(self.constant_data)
        self.assertEqual(len(scores), len(self.constant_data))
        self.assertTrue(np.all(scores == 0))  # No anomalies in constant data
        self.assertTrue(np.all(flags == False))
    
    def test_input_validation(self):
        """Test input validation and error handling."""
        # None input
        with self.assertRaises(ValueError):
            detect_anomalies(None)

        # List input (should work)
        list_data = [1, 2, 3, 4, 5]
        scores, flags = detect_anomalies(list_data)
        self.assertEqual(len(scores), 5)
        self.assertEqual(len(flags), 5)

    def test_online_processing_no_future_leakage(self):
        """Test that online processing doesn't use future data."""
        # Create data with anomaly at the end
        data = np.ones(100)
        data[-1] = 10.0  # Anomaly at the very end

        # Process with batch method (simulates online)
        scores, flags = detect_anomalies(data)

        # The anomaly should be detected even though it's at the end
        # This validates that each point only uses historical data
        self.assertTrue(flags[-1], "Anomaly at end should be detected using only historical data")

        # Test with online detector
        detector = create_online_detector()
        online_flags = []

        for value in data:
            _, flag = detector.process_point(value)
            online_flags.append(flag)

        # Results should be consistent
        self.assertEqual(flags[-1], online_flags[-1])

    def test_online_vs_batch_consistency(self):
        """Test that online and batch processing give consistent results."""
        # Create test data
        np.random.seed(123)
        data = np.random.normal(0, 1, 50)
        data[10] = 5.0
        data[30] = -4.0

        # Batch processing
        batch_scores, batch_flags = detect_anomalies(data)

        # Online processing
        detector = create_online_detector()
        online_scores = []
        online_flags = []

        for value in data:
            score, flag = detector.process_point(value)
            online_scores.append(score)
            online_flags.append(flag)

        online_scores = np.array(online_scores)
        online_flags = np.array(online_flags)

        # Results should be identical (within numerical precision)
        np.testing.assert_array_almost_equal(batch_scores, online_scores, decimal=10)
        np.testing.assert_array_equal(batch_flags, online_flags)

    def test_streaming_state_management(self):
        """Test that streaming detector properly manages state."""
        detector = create_online_detector()

        # Process some data
        data1 = [1, 2, 3, 10, 4, 5]  # Anomaly at position 3
        results1 = []
        for value in data1:
            results1.append(detector.process_point(value))

        # Reset and process again
        detector.reset()
        results2 = []
        for value in data1:
            results2.append(detector.process_point(value))

        # Results should be identical after reset
        self.assertEqual(results1, results2)

    def test_historical_data_only_access(self):
        """Test that modules only access historical data."""
        # Create a custom test to verify no future data access
        data = np.array([1, 2, 3, 100, 4, 5, 6])  # Anomaly at position 3

        # Process point by point and verify each step
        detector = create_online_detector()

        for t in range(len(data)):
            current_value = data[t]

            # Before processing, verify historical data length
            expected_history_length = t
            actual_history_length = len(detector.historical_data)

            self.assertEqual(actual_history_length, expected_history_length,
                           f"At time {t}, historical data should have {expected_history_length} points, got {actual_history_length}")

            # Process the point
            score, flag = detector.process_point(current_value)

            # Verify the point was added to history
            self.assertEqual(len(detector.historical_data), t + 1)
            self.assertEqual(detector.historical_data[-1], current_value)
    
    def test_sampler_module_options(self):
        """Test all sampler module options."""
        # Identity sampling
        config = {"sampler": {"method": "identity"}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
        
        # Uniform sampling
        config = {"sampler": {"method": "uniform", "step_size": 2}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
        
        # Adaptive sampling
        config = {"sampler": {"method": "adaptive", "window_size": 10, "variance_threshold": 0.1}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
    
    def test_denoiser_module_options(self):
        """Test all denoiser module options."""
        # Identity denoising
        config = {"denoiser": {"method": "identity"}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
        
        # Moving average
        config = {"denoiser": {"method": "moving_average", "window_size": 5}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
        
        # Exponential smoothing
        config = {"denoiser": {"method": "exponential", "alpha": 0.3}}
        scores, flags = detect_anomalies(self.normal_data, config)
        self.assertEqual(len(scores), len(self.normal_data))
    
    def test_detector_module_options(self):
        """Test all detector module options."""
        # Z-score detection
        config = {"detector": {"method": "zscore", "threshold": 2.0}}
        scores, flags = detect_anomalies(self.data_with_spikes, config)
        self.assertTrue(np.any(flags))  # Should detect some anomalies
        
        # IQR detection
        config = {"detector": {"method": "iqr", "multiplier": 1.5}}
        scores, flags = detect_anomalies(self.data_with_outliers, config)
        self.assertTrue(np.any(flags))  # Should detect some anomalies
        
        # Change point detection
        config = {"detector": {"method": "changepoint", "window_size": 10, "threshold": 2.0}}
        scores, flags = detect_anomalies(self.data_with_drift, config)
        self.assertTrue(np.any(flags))  # Should detect some anomalies
    
    def test_filter_module_options(self):
        """Test all filter module options."""
        # Identity filtering
        config = {"filter": {"method": "identity"}}
        scores, flags = detect_anomalies(self.data_with_spikes, config)
        original_anomaly_count = np.sum(flags)
        
        # Temporal filtering
        config = {"filter": {"method": "temporal", "min_distance": 10}}
        scores_filtered, flags_filtered = detect_anomalies(self.data_with_spikes, config)
        filtered_anomaly_count = np.sum(flags_filtered)
        self.assertLessEqual(filtered_anomaly_count, original_anomaly_count)
        
        # Pattern matching filtering
        config = {"filter": {"method": "pattern_matching", "pattern_window": 10, "similarity_threshold": 0.8}}
        scores_pattern, flags_pattern = detect_anomalies(self.data_with_spikes, config)
        self.assertEqual(len(scores_pattern), len(self.data_with_spikes))
    
    def test_module_combinations(self):
        """Test different combinations of module configurations."""
        # Combination 1: Uniform sampling + Moving average + Z-score + Temporal filtering
        config = {
            "sampler": {"method": "uniform", "step_size": 2},
            "denoiser": {"method": "moving_average", "window_size": 5},
            "detector": {"method": "zscore", "threshold": 2.5},
            "filter": {"method": "temporal", "min_distance": 5}
        }
        scores, flags = detect_anomalies(self.data_with_spikes, config)
        self.assertEqual(len(scores), len(self.data_with_spikes))
        
        # Combination 2: Adaptive sampling + Exponential smoothing + IQR + Pattern matching
        config = {
            "sampler": {"method": "adaptive", "window_size": 15, "variance_threshold": 0.2},
            "denoiser": {"method": "exponential", "alpha": 0.4},
            "detector": {"method": "iqr", "multiplier": 2.0},
            "filter": {"method": "pattern_matching", "pattern_window": 8}
        }
        scores, flags = detect_anomalies(self.data_with_outliers, config)
        self.assertEqual(len(scores), len(self.data_with_outliers))
    
    def test_synthetic_data_validation(self):
        """Test detection accuracy on synthetic data with known anomalies."""
        # Test spike detection
        spike_positions = [50, 100, 150]
        config = {"detector": {"method": "zscore", "threshold": 2.0}}
        scores, flags = detect_anomalies(self.data_with_spikes, config)
        
        # Check if spikes are detected (allowing some tolerance)
        detected_near_spikes = 0
        tolerance = 5  # Allow detection within 5 points of actual spike
        
        for spike_pos in spike_positions:
            for i in range(max(0, spike_pos - tolerance), min(len(flags), spike_pos + tolerance + 1)):
                if flags[i]:
                    detected_near_spikes += 1
                    break
        
        detection_rate = detected_near_spikes / len(spike_positions)
        self.assertGreater(detection_rate, 0.5)  # At least 50% detection rate
    
    def test_performance_comparison(self):
        """Test performance comparison between different configurations."""
        large_data = np.random.normal(0, 1, 10000)
        
        # Configuration 1: Simple (fast)
        config_simple = {"detector": {"method": "zscore"}}
        
        # Configuration 2: Complex (slower but potentially more accurate)
        config_complex = {
            "sampler": {"method": "adaptive", "window_size": 20},
            "denoiser": {"method": "moving_average", "window_size": 10},
            "detector": {"method": "changepoint", "window_size": 15},
            "filter": {"method": "temporal", "min_distance": 10}
        }
        
        # Measure execution times
        start_time = time.time()
        scores_simple, flags_simple = detect_anomalies(large_data, config_simple)
        simple_time = time.time() - start_time
        
        start_time = time.time()
        scores_complex, flags_complex = detect_anomalies(large_data, config_complex)
        complex_time = time.time() - start_time
        
        # Both should complete successfully
        self.assertEqual(len(scores_simple), len(large_data))
        self.assertEqual(len(scores_complex), len(large_data))
        
        # Print performance results for manual inspection
        print(f"\nPerformance Comparison (10,000 points):")
        print(f"Simple config time: {simple_time:.4f}s")
        print(f"Complex config time: {complex_time:.4f}s")
        print(f"Simple anomalies detected: {np.sum(flags_simple)}")
        print(f"Complex anomalies detected: {np.sum(flags_complex)}")


def run_visual_tests():
    """Run visual tests to manually inspect detection results."""
    print("\n" + "="*50)
    print("VISUAL TESTS - Check plots for detection quality")
    print("="*50)
    
    # Create test data
    np.random.seed(42)
    
    # Test 1: Spike detection
    data_spikes = np.random.normal(0, 0.5, 200)
    spike_positions = [50, 100, 150]
    for pos in spike_positions:
        data_spikes[pos] = 5.0
    
    config = {"detector": {"method": "zscore", "threshold": 2.0}}
    scores, flags = detect_anomalies(data_spikes, config)
    
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(data_spikes, 'b-', label='Data')
    plt.scatter(np.where(flags)[0], data_spikes[flags], color='red', s=50, label='Detected Anomalies')
    plt.title('Spike Detection Test')
    plt.legend()
    
    # Test 2: Outlier detection with IQR
    data_outliers = np.random.normal(0, 1, 200)
    outlier_positions = [25, 75, 125, 175]
    for pos in outlier_positions:
        data_outliers[pos] = np.random.choice([-4, 4])
    
    config = {"detector": {"method": "iqr", "multiplier": 1.5}}
    scores, flags = detect_anomalies(data_outliers, config)
    
    plt.subplot(2, 2, 2)
    plt.plot(data_outliers, 'b-', label='Data')
    plt.scatter(np.where(flags)[0], data_outliers[flags], color='red', s=50, label='Detected Anomalies')
    plt.title('Outlier Detection Test (IQR)')
    plt.legend()
    
    # Test 3: Change point detection
    data_change = np.concatenate([
        np.random.normal(0, 0.5, 100),
        np.random.normal(3, 0.5, 100)
    ])
    
    config = {"detector": {"method": "changepoint", "window_size": 10, "threshold": 2.0}}
    scores, flags = detect_anomalies(data_change, config)
    
    plt.subplot(2, 2, 3)
    plt.plot(data_change, 'b-', label='Data')
    plt.scatter(np.where(flags)[0], data_change[flags], color='red', s=50, label='Detected Anomalies')
    plt.axvline(x=100, color='green', linestyle='--', label='True Change Point')
    plt.title('Change Point Detection Test')
    plt.legend()
    
    # Test 4: Complex pipeline
    config_complex = {
        "denoiser": {"method": "moving_average", "window_size": 5},
        "detector": {"method": "zscore", "threshold": 2.5},
        "filter": {"method": "temporal", "min_distance": 10}
    }
    scores, flags = detect_anomalies(data_spikes, config_complex)
    
    plt.subplot(2, 2, 4)
    plt.plot(data_spikes, 'b-', alpha=0.7, label='Original Data')
    plt.plot(scores * np.max(data_spikes), 'g-', alpha=0.7, label='Anomaly Scores')
    plt.scatter(np.where(flags)[0], data_spikes[flags], color='red', s=50, label='Final Detections')
    plt.title('Complex Pipeline Test')
    plt.legend()
    
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    # Run unit tests
    print("Running comprehensive test suite...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run visual tests
    run_visual_tests()
