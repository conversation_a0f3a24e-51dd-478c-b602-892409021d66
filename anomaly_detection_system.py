"""
Comprehensive Online Anomaly Detection System with Modular Architecture

This module provides a configurable anomaly detection pipeline designed for real-time/streaming
processing with four main components:
1. Sampler: Historical data sampling (only uses data from time steps [0, 1, ..., t-1] for time t)
2. Denoiser: Signal smoothing and noise reduction
3. Detector: Core anomaly detection algorithms
4. Filter: Post-processing and false positive reduction

The system processes data point-by-point in an online fashion, ensuring no future data leakage.
Each time step t only has access to historical data from previous time steps.

Author: Anomaly Detection System
Date: 2025-08-12
"""

import numpy as np
from typing import Tuple, List, Union, Dict, Any, Optional
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')


class BaseModule(ABC):
    """Base class for all anomaly detection modules."""

    def __init__(self):
        """Initialize the module with empty state."""
        self.reset_state()

    @abstractmethod
    def reset_state(self):
        """Reset the internal state of the module."""
        pass

    @abstractmethod
    def process_point(self, current_value: float, historical_data: np.ndarray, time_step: int, **kwargs) -> Any:
        """
        Process a single data point using only historical data.

        Args:
            current_value: The current data point value
            historical_data: Array of historical values [0, 1, ..., t-1]
            time_step: Current time step t
            **kwargs: Additional parameters

        Returns:
            Processed result for the current time step
        """
        pass


class SamplerModule(BaseModule):
    """
    Historical data sampling module for online processing.

    At each time step t, samples from historical data [0, 1, ..., t-1] to reduce
    computational overhead while preserving important information for anomaly detection.
    """

    def __init__(self, method: str = "identity", **params):
        """
        Initialize sampler module.

        Args:
            method: Sampling method ("identity", "uniform", "adaptive")
            **params: Method-specific parameters
        """
        super().__init__()
        self.method = method
        self.params = params

    def reset_state(self):
        """Reset the internal state of the sampler."""
        self.variance_history = []  # For adaptive sampling
        self.last_sampled_indices = []  # Track what was sampled

    def process_point(self, current_value: float, historical_data: np.ndarray, time_step: int, **kwargs) -> np.ndarray:
        """
        Sample from historical data for processing the current time step.

        Args:
            current_value: Current data point (not used in sampling, only for interface consistency)
            historical_data: Historical values [0, 1, ..., t-1]
            time_step: Current time step t

        Returns:
            Sampled historical data array
        """
        if len(historical_data) == 0:
            return np.array([])

        if self.method == "identity":
            return self._identity_sampling(historical_data, time_step)
        elif self.method == "uniform":
            return self._uniform_sampling(historical_data, time_step)
        elif self.method == "adaptive":
            return self._adaptive_sampling(historical_data, time_step)
        else:
            raise ValueError(f"Unknown sampling method: {self.method}")

    def _identity_sampling(self, historical_data: np.ndarray, time_step: int) -> np.ndarray:
        """No sampling - return all historical data."""
        return historical_data.copy()

    def _uniform_sampling(self, historical_data: np.ndarray, time_step: int) -> np.ndarray:
        """Uniform sampling with configurable step size from historical data (vectorized)."""
        step_size = self.params.get("step_size", 2)
        max_history = self.params.get("max_history", None)

        if step_size <= 1:
            sampled_data = historical_data.copy()
        else:
            # Vectorized sampling: create indices array and sample
            sampled_indices = np.arange(0, len(historical_data), step_size)

            # Always include the most recent point if not already included
            if len(historical_data) > 0 and (len(historical_data) - 1) not in sampled_indices:
                sampled_indices = np.append(sampled_indices, len(historical_data) - 1)

            sampled_data = historical_data[sampled_indices]

        # Limit history length if specified (vectorized slicing)
        if max_history is not None and len(sampled_data) > max_history:
            sampled_data = sampled_data[-max_history:]

        return sampled_data

    def _adaptive_sampling(self, historical_data: np.ndarray, time_step: int) -> np.ndarray:
        """Adaptive sampling based on local variance in historical data (vectorized)."""
        window_size = self.params.get("window_size", 10)
        variance_threshold = self.params.get("variance_threshold", 0.1)
        max_history = self.params.get("max_history", 200)

        if len(historical_data) <= window_size:
            return historical_data.copy()

        # Vectorized approach: compute variances for all windows at once
        data_length = len(historical_data)
        sampled_indices = [0]  # Always include first point

        # Create sliding windows and compute variances vectorized
        window_starts = np.arange(window_size, data_length, window_size // 2)

        for window_start in window_starts:
            window_end = min(window_start + window_size, data_length)
            window_data = historical_data[window_start:window_end]
            variance = np.var(window_data)

            if variance > variance_threshold:
                # High variance region - sample more densely
                step = max(1, window_size // 3)
            else:
                # Low variance region - sample sparsely
                step = window_size

            # Vectorized index generation for this window
            window_indices = np.arange(window_start, window_end, step)
            sampled_indices.extend(window_indices.tolist())

        # Always include the most recent point
        if data_length > 0 and (data_length - 1) not in sampled_indices:
            sampled_indices.append(data_length - 1)

        # Vectorized operations: unique, sort, and index
        sampled_indices = np.unique(np.array(sampled_indices))
        sampled_data = historical_data[sampled_indices]

        # Limit history length (vectorized slicing)
        if len(sampled_data) > max_history:
            sampled_data = sampled_data[-max_history:]

        return sampled_data


class DenoiserModule(BaseModule):
    """
    Signal smoothing module for online processing.

    Applies denoising to the current value using only historical sampled data.
    Maintains internal state for methods that require memory (e.g., exponential smoothing).
    """

    def __init__(self, method: str = "identity", **params):
        """
        Initialize denoiser module.

        Args:
            method: Denoising method ("identity", "moving_average", "exponential")
            **params: Method-specific parameters
        """
        super().__init__()
        self.method = method
        self.params = params

    def reset_state(self):
        """Reset the internal state of the denoiser."""
        self.exponential_state = None  # For exponential smoothing
        self.moving_avg_buffer = []    # For moving average

    def process_point(self, current_value: float, historical_data: np.ndarray, time_step: int, **kwargs) -> float:
        """
        Apply denoising to current value using historical data.

        Args:
            current_value: Current data point value
            historical_data: Sampled historical values
            time_step: Current time step

        Returns:
            Denoised current value
        """
        if self.method == "identity":
            return self._identity_denoising(current_value, historical_data, time_step)
        elif self.method == "moving_average":
            return self._moving_average_denoising(current_value, historical_data, time_step)
        elif self.method == "exponential":
            return self._exponential_smoothing(current_value, historical_data, time_step)
        else:
            raise ValueError(f"Unknown denoising method: {self.method}")

    def _identity_denoising(self, current_value: float, historical_data: np.ndarray, time_step: int) -> float:
        """No denoising - return current value unchanged."""
        return current_value

    def _moving_average_denoising(self, current_value: float, historical_data: np.ndarray, time_step: int) -> float:
        """Moving average smoothing using recent historical values (vectorized)."""
        window_size = self.params.get("window_size", 5)

        if len(historical_data) == 0:
            return current_value

        # Vectorized approach: slice recent values and compute mean
        start_idx = max(0, len(historical_data) - (window_size - 1))
        recent_values = historical_data[start_idx:]

        # Vectorized concatenation and mean calculation
        all_values = np.append(recent_values, current_value)

        return np.mean(all_values)

    def _exponential_smoothing(self, current_value: float, historical_data: np.ndarray, time_step: int) -> float:
        """Exponential smoothing maintaining state across time steps."""
        alpha = self.params.get("alpha", 0.3)

        if time_step == 0 or self.exponential_state is None:
            # Initialize with current value
            self.exponential_state = current_value
            return current_value

        # Apply exponential smoothing
        smoothed_value = alpha * current_value + (1 - alpha) * self.exponential_state
        self.exponential_state = smoothed_value

        return smoothed_value


class DetectorModule(BaseModule):
    """
    Core anomaly detection module for online processing.

    Detects anomalies in the current value using only historical sampled data.
    Maintains internal state for methods that require memory across time steps.
    """

    def __init__(self, method: str = "zscore", **params):
        """
        Initialize detector module.

        Args:
            method: Detection method ("zscore", "iqr", "changepoint", "diff")
            **params: Method-specific parameters
        """
        super().__init__()
        self.method = method
        self.params = params

    def reset_state(self):
        """Reset the internal state of the detector."""
        self.running_stats = {"mean": 0.0, "var": 0.0, "count": 0}  # For online statistics
        self.recent_values = []  # For change point detection
        self.percentile_buffer = []  # For IQR calculation

    def process_point(self, current_value: float, historical_data: np.ndarray, time_step: int, **kwargs) -> Tuple[float, bool]:
        """
        Detect anomaly in current value using historical data.

        Args:
            current_value: Current data point value
            historical_data: Sampled historical values
            time_step: Current time step

        Returns:
            Tuple of (anomaly_score, binary_flag)
        """
        if self.method == "zscore":
            return self._zscore_detection(current_value, historical_data, time_step)
        elif self.method == "iqr":
            return self._iqr_detection(current_value, historical_data, time_step)
        elif self.method == "changepoint":
            return self._changepoint_detection(current_value, historical_data, time_step)
        elif self.method == "diff":
            return self._diff_detection(current_value, historical_data, time_step)
        else:
            raise ValueError(f"Unknown detection method: {self.method}")

    def _zscore_detection(self, current_value: float, historical_data: np.ndarray, time_step: int) -> Tuple[float, bool]:
        """Z-score based anomaly detection using historical data (vectorized)."""
        threshold = self.params.get("threshold", 3.0)
        window_size = self.params.get("window_size", None)
        min_samples = self.params.get("min_samples", 5)

        if len(historical_data) < min_samples:
            # Not enough historical data for reliable statistics
            return 0.0, False

        # Vectorized data selection
        if window_size is None or window_size >= len(historical_data):
            reference_data = historical_data
        else:
            reference_data = historical_data[-window_size:]

        # Vectorized statistical calculations
        mean_val = np.mean(reference_data)
        std_val = np.std(reference_data, ddof=0)  # Population standard deviation

        if std_val == 0:
            # No variance in historical data
            score = 1.0 if current_value != mean_val else 0.0
            flag = current_value != mean_val
        else:
            # Vectorized z-score calculation
            z_score = np.abs((current_value - mean_val) / std_val)
            score = np.minimum(z_score / threshold, 1.0)
            flag = z_score > threshold

        return float(score), bool(flag)

    def _iqr_detection(self, current_value: float, historical_data: np.ndarray, time_step: int) -> Tuple[float, bool]:
        """Interquartile range based anomaly detection using historical data (vectorized)."""
        multiplier = self.params.get("multiplier", 1.5)
        min_samples = self.params.get("min_samples", 10)

        if len(historical_data) < min_samples:
            # Not enough historical data for reliable percentiles
            return 0.0, False

        # Vectorized percentile calculation
        q1, q3 = np.percentile(historical_data, [25, 75])
        iqr = q3 - q1

        if iqr == 0:
            # No variance in historical data
            score = 1.0 if current_value != q1 else 0.0
            flag = current_value != q1
            return score, flag

        # Vectorized bound calculations
        lower_bound = q1 - multiplier * iqr
        upper_bound = q3 + multiplier * iqr

        # Vectorized distance and score calculations
        lower_distance = np.maximum(lower_bound - current_value, 0)
        upper_distance = np.maximum(current_value - upper_bound, 0)

        # Use the maximum distance (only one will be non-zero)
        distance = np.maximum(lower_distance, upper_distance)
        score = np.minimum(distance / iqr, 1.0)
        flag = distance > 0

        return float(score), bool(flag)

    def _changepoint_detection(self, current_value: float, historical_data: np.ndarray, time_step: int) -> Tuple[float, bool]:
        """Change point detection using recent historical values (vectorized)."""
        window_size = self.params.get("window_size", 10)
        threshold = self.params.get("threshold", 2.0)
        min_samples = self.params.get("min_samples", 20)

        # Update recent values buffer
        self.recent_values.append(current_value)
        max_buffer_size = window_size * 3  # Keep enough for comparison
        if len(self.recent_values) > max_buffer_size:
            self.recent_values.pop(0)

        if len(historical_data) < min_samples or len(self.recent_values) < window_size * 2:
            # Not enough data for change point detection
            return 0.0, False

        # Vectorized window extraction and statistical calculations
        recent_values_array = np.array(self.recent_values)
        recent_window = recent_values_array[-window_size:]
        earlier_window = recent_values_array[-window_size*2:-window_size]

        # Vectorized statistical calculations
        recent_stats = np.array([np.mean(recent_window), np.std(recent_window)])
        earlier_stats = np.array([np.mean(earlier_window), np.std(earlier_window)])

        recent_mean, recent_std = recent_stats
        earlier_mean, earlier_std = earlier_stats

        # Vectorized change magnitude calculation
        mean_change = np.abs(recent_mean - earlier_mean)
        pooled_std = np.sqrt((recent_std**2 + earlier_std**2) / 2)

        if pooled_std == 0:
            # No variance to compare
            score = 1.0 if mean_change > 0 else 0.0
            flag = mean_change > 0
        else:
            change_score = mean_change / pooled_std
            score = np.minimum(change_score / threshold, 1.0)
            flag = change_score > threshold

        return float(score), bool(flag)

    def _diff_detection(self, current_value: float, historical_data: np.ndarray, time_step: int) -> Tuple[float, bool]:
        """
        Difference-based anomaly detection using most recent historical value.

        Calculates anomaly score as the absolute difference between current value
        and the most recent historical value, normalized by a configurable threshold.

        Args:
            current_value: Current data point value
            historical_data: Historical values [0, 1, ..., t-1]
            time_step: Current time step (not used but kept for interface consistency)

        Returns:
            Tuple of (anomaly_score, binary_flag)
        """
        threshold = self.params.get("threshold", 1.0)

        if len(historical_data) == 0:
            # No historical data available, cannot compute difference
            return 0.0, False

        # Get the most recent historical value
        most_recent_value = historical_data[-1]

        # Calculate absolute difference
        abs_diff = abs(current_value - most_recent_value)

        # Normalize score to [0, 1] range using threshold
        score = min(abs_diff / threshold, 1.0) if threshold > 0 else 0.0

        # Binary flag based on whether difference exceeds threshold
        flag = abs_diff > threshold

        return score, flag


class FilterModule(BaseModule):
    """
    Post-processing module for filtering false positives in online processing.

    Applies filtering to current anomaly detection results using historical context.
    Maintains state for temporal and pattern-based filtering across time steps.
    """

    def __init__(self, method: str = "identity", **params):
        """
        Initialize filter module.

        Args:
            method: Filtering method ("identity", "temporal", "pattern_matching")
            **params: Method-specific parameters
        """
        super().__init__()
        self.method = method
        self.params = params

    def reset_state(self):
        """Reset the internal state of the filter."""
        self.last_anomaly_time = -1  # For temporal filtering
        self.historical_patterns = []  # For pattern matching
        self.recent_patterns = []  # Buffer for pattern extraction

    def process_point(self, current_value: float, score: float, flag: bool,
                     historical_data: np.ndarray, time_step: int, **kwargs) -> Tuple[float, bool]:
        """
        Apply filtering to current anomaly detection results.

        Args:
            current_value: Current data point value
            score: Anomaly score from detector
            flag: Binary anomaly flag from detector
            historical_data: Historical data used for context
            time_step: Current time step

        Returns:
            Tuple of (filtered_score, filtered_flag)
        """
        if self.method == "identity":
            return self._identity_filtering(score, flag, time_step)
        elif self.method == "temporal":
            return self._temporal_filtering(score, flag, time_step)
        elif self.method == "pattern_matching":
            return self._pattern_matching_filtering(current_value, score, flag, historical_data, time_step)
        else:
            raise ValueError(f"Unknown filtering method: {self.method}")

    def _identity_filtering(self, score: float, flag: bool, time_step: int) -> Tuple[float, bool]:
        """No filtering - return original score and flag."""
        return score, flag

    def _temporal_filtering(self, score: float, flag: bool, time_step: int) -> Tuple[float, bool]:
        """Filter anomalies that are too close in time."""
        min_distance = self.params.get("min_distance", 5)

        if not flag:
            # Not an anomaly, no filtering needed
            return score, flag

        # Check if enough time has passed since last anomaly
        if time_step - self.last_anomaly_time < min_distance:
            # Too close to previous anomaly, suppress this one
            filtered_score = score * 0.5  # Reduce but don't eliminate
            filtered_flag = False
        else:
            # Far enough from previous anomaly, keep it
            filtered_score = score
            filtered_flag = flag
            self.last_anomaly_time = time_step

        return filtered_score, filtered_flag

    def _pattern_matching_filtering(self, current_value: float, score: float, flag: bool,
                                   historical_data: np.ndarray, time_step: int) -> Tuple[float, bool]:
        """Filter anomalies that match known historical patterns (vectorized)."""
        pattern_window = self.params.get("pattern_window", 10)
        similarity_threshold = self.params.get("similarity_threshold", 0.8)
        max_patterns = self.params.get("max_patterns", 100)

        # Update recent patterns buffer
        self.recent_patterns.append(current_value)
        if len(self.recent_patterns) > pattern_window * 2:
            self.recent_patterns.pop(0)

        if not flag or len(self.recent_patterns) < pattern_window:
            # Not an anomaly or not enough data for pattern matching
            return score, flag

        # Extract current pattern around the anomaly
        current_pattern = np.array(self.recent_patterns[-pattern_window:])

        # Vectorized similarity checking with historical patterns
        is_known_pattern = False
        if self.historical_patterns:
            # Convert historical patterns to array for vectorized operations
            valid_patterns = [p for p in self.historical_patterns if len(p) == len(current_pattern)]

            if valid_patterns:
                # Stack patterns for vectorized correlation calculation
                patterns_array = np.array(valid_patterns)

                # Vectorized correlation calculation
                try:
                    # Calculate correlations for all patterns at once
                    correlations = np.array([
                        np.corrcoef(current_pattern, pattern)[0, 1]
                        for pattern in patterns_array
                    ])

                    # Check if any correlation exceeds threshold
                    valid_correlations = correlations[~np.isnan(correlations)]
                    if len(valid_correlations) > 0:
                        max_correlation = np.max(np.abs(valid_correlations))
                        is_known_pattern = max_correlation > similarity_threshold

                except:
                    # Handle edge cases in correlation calculation
                    is_known_pattern = False

        if is_known_pattern:
            # Known pattern, suppress the anomaly
            filtered_score = score * 0.3  # Significantly reduce score
            filtered_flag = False
        else:
            # New pattern, keep the anomaly and add pattern to history
            filtered_score = score
            filtered_flag = flag

            # Add new pattern to historical patterns
            self.historical_patterns.append(current_pattern.copy())
            if len(self.historical_patterns) > max_patterns:
                self.historical_patterns.pop(0)  # Remove oldest pattern

        return filtered_score, filtered_flag


def detect_anomalies(
    data: Union[np.ndarray, List[float]],
    config: Dict[str, Any] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Main online anomaly detection function that processes data point-by-point.

    This function simulates real-time processing where each time step t only has access
    to historical data from time steps [0, 1, ..., t-1]. No future data is used.

    Args:
        data: One-dimensional time series data (numpy array or list of numerical values)
        config: Configuration dictionary for all modules

    Returns:
        Tuple containing:
        - anomaly_scores: Array of float values (0-1) indicating anomaly likelihood
        - binary_flags: Array of boolean values (True = anomaly detected)
    """
    # Input validation
    if data is None:
        raise ValueError("Input data cannot be None")

    # Convert to numpy array
    if isinstance(data, list):
        data = np.array(data, dtype=float)
    elif not isinstance(data, np.ndarray):
        data = np.array(data, dtype=float)

    # Handle empty or single-point data
    if len(data) == 0:
        return np.array([]), np.array([], dtype=bool)
    elif len(data) == 1:
        return np.array([0.0]), np.array([False])

    # Default configuration
    if config is None:
        config = {}

    # Initialize modules with configuration
    sampler_config = config.get("sampler", {"method": "identity"})
    denoiser_config = config.get("denoiser", {"method": "identity"})
    detector_config = config.get("detector", {"method": "zscore"})
    filter_config = config.get("filter", {"method": "identity"})

    sampler = SamplerModule(**sampler_config)
    denoiser = DenoiserModule(**denoiser_config)
    detector = DetectorModule(**detector_config)
    filter_module = FilterModule(**filter_config)

    # Initialize result arrays
    scores = np.zeros(len(data))
    flags = np.zeros(len(data), dtype=bool)

    # Process data point-by-point in online fashion
    for t in range(len(data)):
        current_value = data[t]

        # Historical data: only values from time steps [0, 1, ..., t-1]
        historical_data = data[:t] if t > 0 else np.array([])

        # Step 1: Sample from historical data
        sampled_historical = sampler.process_point(current_value, historical_data, t)

        # Step 2: Denoise current value using sampled historical data
        denoised_value = denoiser.process_point(current_value, sampled_historical, t)

        # Step 3: Detect anomaly in denoised current value
        score, flag = detector.process_point(denoised_value, sampled_historical, t)

        # Step 4: Filter the detection result
        filtered_score, filtered_flag = filter_module.process_point(
            denoised_value, score, flag, sampled_historical, t
        )

        # Store results
        scores[t] = filtered_score
        flags[t] = filtered_flag

    return scores, flags


def create_online_detector(config: Dict[str, Any] = None):
    """
    Create a stateful online anomaly detector for streaming data.

    This function returns a detector object that can process data points one at a time
    while maintaining internal state across calls.

    Args:
        config: Configuration dictionary for all modules

    Returns:
        OnlineAnomalyDetector instance
    """
    return OnlineAnomalyDetector(config)


class OnlineAnomalyDetector:
    """
    Stateful online anomaly detector for streaming data processing.

    This class maintains internal state across multiple calls to process_point(),
    making it suitable for real-time anomaly detection applications.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the online detector with configuration."""
        if config is None:
            config = {}

        # Initialize modules
        sampler_config = config.get("sampler", {"method": "identity"})
        denoiser_config = config.get("denoiser", {"method": "identity"})
        detector_config = config.get("detector", {"method": "zscore"})
        filter_config = config.get("filter", {"method": "identity"})

        self.sampler = SamplerModule(**sampler_config)
        self.denoiser = DenoiserModule(**denoiser_config)
        self.detector = DetectorModule(**detector_config)
        self.filter_module = FilterModule(**filter_config)

        # Initialize state
        self.reset()

    def reset(self):
        """Reset the detector state."""
        self.historical_data = []
        self.time_step = 0

        # Reset module states
        self.sampler.reset_state()
        self.denoiser.reset_state()
        self.detector.reset_state()
        self.filter_module.reset_state()

    def process_point(self, value: float) -> Tuple[float, bool]:
        """
        Process a single data point and return anomaly score and flag.

        Args:
            value: Current data point value

        Returns:
            Tuple of (anomaly_score, binary_flag)
        """
        current_value = float(value)

        # Get historical data (only previous time steps)
        historical_array = np.array(self.historical_data) if self.historical_data else np.array([])

        # Process through pipeline
        sampled_historical = self.sampler.process_point(current_value, historical_array, self.time_step)
        denoised_value = self.denoiser.process_point(current_value, sampled_historical, self.time_step)
        score, flag = self.detector.process_point(denoised_value, sampled_historical, self.time_step)
        filtered_score, filtered_flag = self.filter_module.process_point(
            denoised_value, score, flag, sampled_historical, self.time_step
        )

        # Update state
        self.historical_data.append(current_value)
        self.time_step += 1

        # Limit historical data size to prevent memory issues
        max_history = 10000  # Configurable limit
        if len(self.historical_data) > max_history:
            self.historical_data.pop(0)

        return filtered_score, filtered_flag
