"""
Comprehensive Anomaly Detection System with Modular Architecture

This module provides a configurable anomaly detection pipeline with four main components:
1. Sampler: Data compression/downsampling
2. Denoiser: Signal smoothing and noise reduction
3. Detector: Core anomaly detection algorithms
4. Filter: Post-processing and false positive reduction

Author: Anomaly Detection System
Date: 2025-08-12
"""

import numpy as np
from typing import Tuple, List, Union, Dict, Any
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')


class BaseModule(ABC):
    """Base class for all anomaly detection modules."""
    
    @abstractmethod
    def process(self, data: np.ndarray, **kwargs) -> np.ndarray:
        """Process input data and return transformed data."""
        pass


class SamplerModule(BaseModule):
    """Data compression module with multiple sampling strategies."""
    
    def __init__(self, method: str = "identity", **params):
        """
        Initialize sampler module.
        
        Args:
            method: Sampling method ("identity", "uniform", "adaptive")
            **params: Method-specific parameters
        """
        self.method = method
        self.params = params
    
    def process(self, data: np.ndarray, **kwargs) -> np.ndarray:
        """Apply sampling to input data."""
        if self.method == "identity":
            return self._identity_sampling(data)
        elif self.method == "uniform":
            return self._uniform_sampling(data)
        elif self.method == "adaptive":
            return self._adaptive_sampling(data)
        else:
            raise ValueError(f"Unknown sampling method: {self.method}")
    
    def _identity_sampling(self, data: np.ndarray) -> np.ndarray:
        """No sampling - return original data."""
        return data.copy()
    
    def _uniform_sampling(self, data: np.ndarray) -> np.ndarray:
        """Uniform downsampling with configurable step size."""
        step_size = self.params.get("step_size", 2)
        if step_size <= 1:
            return data.copy()
        return data[::step_size]
    
    def _adaptive_sampling(self, data: np.ndarray) -> np.ndarray:
        """Adaptive sampling based on data variance."""
        window_size = self.params.get("window_size", 10)
        variance_threshold = self.params.get("variance_threshold", 0.1)
        
        if len(data) <= window_size:
            return data.copy()
        
        sampled_indices = [0]  # Always include first point
        
        for i in range(window_size, len(data), window_size):
            window = data[max(0, i-window_size):i]
            variance = np.var(window)
            
            if variance > variance_threshold:
                # High variance region - sample more densely
                step = max(1, window_size // 3)
            else:
                # Low variance region - sample sparsely
                step = window_size
            
            sampled_indices.extend(range(i-window_size, min(i, len(data)), step))
        
        # Always include last point
        if sampled_indices[-1] != len(data) - 1:
            sampled_indices.append(len(data) - 1)
        
        return data[sorted(set(sampled_indices))]


class DenoiserModule(BaseModule):
    """Signal smoothing module with multiple denoising strategies."""
    
    def __init__(self, method: str = "identity", **params):
        """
        Initialize denoiser module.
        
        Args:
            method: Denoising method ("identity", "moving_average", "exponential")
            **params: Method-specific parameters
        """
        self.method = method
        self.params = params
    
    def process(self, data: np.ndarray, **kwargs) -> np.ndarray:
        """Apply denoising to input data."""
        if self.method == "identity":
            return self._identity_denoising(data)
        elif self.method == "moving_average":
            return self._moving_average_denoising(data)
        elif self.method == "exponential":
            return self._exponential_smoothing(data)
        else:
            raise ValueError(f"Unknown denoising method: {self.method}")
    
    def _identity_denoising(self, data: np.ndarray) -> np.ndarray:
        """No denoising - return original data."""
        return data.copy()
    
    def _moving_average_denoising(self, data: np.ndarray) -> np.ndarray:
        """Moving average smoothing with configurable window size."""
        window_size = self.params.get("window_size", 5)
        
        if window_size <= 1 or len(data) <= window_size:
            return data.copy()
        
        smoothed = np.zeros_like(data)
        half_window = window_size // 2
        
        for i in range(len(data)):
            start_idx = max(0, i - half_window)
            end_idx = min(len(data), i + half_window + 1)
            smoothed[i] = np.mean(data[start_idx:end_idx])
        
        return smoothed
    
    def _exponential_smoothing(self, data: np.ndarray) -> np.ndarray:
        """Exponential smoothing with configurable alpha parameter."""
        alpha = self.params.get("alpha", 0.3)
        
        if len(data) == 0:
            return data.copy()
        
        smoothed = np.zeros_like(data)
        smoothed[0] = data[0]
        
        for i in range(1, len(data)):
            smoothed[i] = alpha * data[i] + (1 - alpha) * smoothed[i-1]
        
        return smoothed


class DetectorModule(BaseModule):
    """Core anomaly detection module with multiple detection strategies."""
    
    def __init__(self, method: str = "zscore", **params):
        """
        Initialize detector module.
        
        Args:
            method: Detection method ("zscore", "iqr", "changepoint")
            **params: Method-specific parameters
        """
        self.method = method
        self.params = params
    
    def process(self, data: np.ndarray, **kwargs) -> Tuple[np.ndarray, np.ndarray]:
        """
        Apply anomaly detection to input data.
        
        Returns:
            Tuple of (anomaly_scores, binary_flags)
        """
        if self.method == "zscore":
            return self._zscore_detection(data)
        elif self.method == "iqr":
            return self._iqr_detection(data)
        elif self.method == "changepoint":
            return self._changepoint_detection(data)
        else:
            raise ValueError(f"Unknown detection method: {self.method}")
    
    def _zscore_detection(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Z-score based anomaly detection."""
        threshold = self.params.get("threshold", 3.0)
        window_size = self.params.get("window_size", None)
        
        if len(data) == 0:
            return np.array([]), np.array([], dtype=bool)
        
        scores = np.zeros(len(data))
        flags = np.zeros(len(data), dtype=bool)
        
        if window_size is None or window_size >= len(data):
            # Global statistics
            mean_val = np.mean(data)
            std_val = np.std(data)
            if std_val > 0:
                z_scores = np.abs((data - mean_val) / std_val)
                scores = np.clip(z_scores / threshold, 0, 1)
                flags = z_scores > threshold
        else:
            # Rolling window statistics
            half_window = window_size // 2
            for i in range(len(data)):
                start_idx = max(0, i - half_window)
                end_idx = min(len(data), i + half_window + 1)
                window_data = data[start_idx:end_idx]
                
                mean_val = np.mean(window_data)
                std_val = np.std(window_data)
                
                if std_val > 0:
                    z_score = abs((data[i] - mean_val) / std_val)
                    scores[i] = min(z_score / threshold, 1.0)
                    flags[i] = z_score > threshold
        
        return scores, flags
    
    def _iqr_detection(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Interquartile range based anomaly detection."""
        multiplier = self.params.get("multiplier", 1.5)
        
        if len(data) == 0:
            return np.array([]), np.array([], dtype=bool)
        
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        
        if iqr == 0:
            return np.zeros(len(data)), np.zeros(len(data), dtype=bool)
        
        lower_bound = q1 - multiplier * iqr
        upper_bound = q3 + multiplier * iqr
        
        # Calculate scores based on distance from bounds
        scores = np.zeros(len(data))
        flags = np.zeros(len(data), dtype=bool)
        
        for i, value in enumerate(data):
            if value < lower_bound:
                distance = lower_bound - value
                scores[i] = min(distance / iqr, 1.0)
                flags[i] = True
            elif value > upper_bound:
                distance = value - upper_bound
                scores[i] = min(distance / iqr, 1.0)
                flags[i] = True
        
        return scores, flags
    
    def _changepoint_detection(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Simple change point detection based on local variance."""
        window_size = self.params.get("window_size", 10)
        threshold = self.params.get("threshold", 2.0)
        
        if len(data) <= window_size:
            return np.zeros(len(data)), np.zeros(len(data), dtype=bool)
        
        scores = np.zeros(len(data))
        flags = np.zeros(len(data), dtype=bool)
        
        for i in range(window_size, len(data) - window_size):
            left_window = data[i-window_size:i]
            right_window = data[i:i+window_size]
            
            left_mean = np.mean(left_window)
            right_mean = np.mean(right_window)
            left_std = np.std(left_window)
            right_std = np.std(right_window)
            
            # Calculate change magnitude
            mean_change = abs(right_mean - left_mean)
            pooled_std = np.sqrt((left_std**2 + right_std**2) / 2)
            
            if pooled_std > 0:
                change_score = mean_change / pooled_std
                scores[i] = min(change_score / threshold, 1.0)
                flags[i] = change_score > threshold
        
        return scores, flags


class FilterModule(BaseModule):
    """Post-processing module for filtering false positives."""

    def __init__(self, method: str = "identity", **params):
        """
        Initialize filter module.

        Args:
            method: Filtering method ("identity", "temporal", "pattern_matching")
            **params: Method-specific parameters
        """
        self.method = method
        self.params = params
        self.historical_patterns = []

    def process(self, data: np.ndarray, scores: np.ndarray, flags: np.ndarray, **kwargs) -> Tuple[np.ndarray, np.ndarray]:
        """
        Apply filtering to anomaly scores and flags.

        Args:
            data: Original time series data
            scores: Anomaly scores from detector
            flags: Binary anomaly flags from detector

        Returns:
            Tuple of (filtered_scores, filtered_flags)
        """
        if self.method == "identity":
            return self._identity_filtering(scores, flags)
        elif self.method == "temporal":
            return self._temporal_filtering(scores, flags)
        elif self.method == "pattern_matching":
            return self._pattern_matching_filtering(data, scores, flags)
        else:
            raise ValueError(f"Unknown filtering method: {self.method}")

    def _identity_filtering(self, scores: np.ndarray, flags: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """No filtering - return original scores and flags."""
        return scores.copy(), flags.copy()

    def _temporal_filtering(self, scores: np.ndarray, flags: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Filter anomalies that are too close in time."""
        min_distance = self.params.get("min_distance", 5)

        filtered_scores = scores.copy()
        filtered_flags = flags.copy()

        if len(flags) == 0:
            return filtered_scores, filtered_flags

        # Find anomaly indices
        anomaly_indices = np.where(flags)[0]

        if len(anomaly_indices) <= 1:
            return filtered_scores, filtered_flags

        # Keep only anomalies that are far enough apart
        keep_indices = [anomaly_indices[0]]  # Always keep first anomaly

        for i in range(1, len(anomaly_indices)):
            current_idx = anomaly_indices[i]
            last_kept_idx = keep_indices[-1]

            if current_idx - last_kept_idx >= min_distance:
                keep_indices.append(current_idx)
            else:
                # Keep the one with higher score
                if scores[current_idx] > scores[last_kept_idx]:
                    keep_indices[-1] = current_idx

        # Reset flags and adjust scores
        filtered_flags.fill(False)
        for idx in keep_indices:
            filtered_flags[idx] = True

        # Suppress scores for filtered out anomalies
        for idx in anomaly_indices:
            if idx not in keep_indices:
                filtered_scores[idx] *= 0.5  # Reduce but don't eliminate

        return filtered_scores, filtered_flags

    def _pattern_matching_filtering(self, data: np.ndarray, scores: np.ndarray, flags: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Filter anomalies that match known historical patterns."""
        pattern_window = self.params.get("pattern_window", 10)
        similarity_threshold = self.params.get("similarity_threshold", 0.8)

        filtered_scores = scores.copy()
        filtered_flags = flags.copy()

        if len(data) < pattern_window:
            return filtered_scores, filtered_flags

        anomaly_indices = np.where(flags)[0]

        for idx in anomaly_indices:
            # Extract pattern around anomaly
            start_idx = max(0, idx - pattern_window // 2)
            end_idx = min(len(data), idx + pattern_window // 2 + 1)
            current_pattern = data[start_idx:end_idx]

            # Check similarity with historical patterns
            is_known_pattern = False
            for historical_pattern in self.historical_patterns:
                if len(historical_pattern) == len(current_pattern):
                    # Calculate normalized correlation
                    correlation = np.corrcoef(current_pattern, historical_pattern)[0, 1]
                    if not np.isnan(correlation) and abs(correlation) > similarity_threshold:
                        is_known_pattern = True
                        break

            if is_known_pattern:
                filtered_flags[idx] = False
                filtered_scores[idx] *= 0.3  # Significantly reduce score

        # Add new patterns to historical patterns (limit size)
        max_patterns = self.params.get("max_patterns", 100)
        for idx in anomaly_indices:
            if filtered_flags[idx]:  # Only add patterns that weren't filtered out
                start_idx = max(0, idx - pattern_window // 2)
                end_idx = min(len(data), idx + pattern_window // 2 + 1)
                pattern = data[start_idx:end_idx]

                self.historical_patterns.append(pattern)
                if len(self.historical_patterns) > max_patterns:
                    self.historical_patterns.pop(0)  # Remove oldest pattern

        return filtered_scores, filtered_flags


def detect_anomalies(
    data: Union[np.ndarray, List[float]],
    config: Dict[str, Any] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Main anomaly detection function that orchestrates all modules.

    Args:
        data: One-dimensional time series data (numpy array or list of numerical values)
        config: Configuration dictionary for all modules

    Returns:
        Tuple containing:
        - anomaly_scores: Array of float values (0-1) indicating anomaly likelihood
        - binary_flags: Array of boolean values (True = anomaly detected)
    """
    # Input validation
    if data is None:
        raise ValueError("Input data cannot be None")

    # Convert to numpy array
    if isinstance(data, list):
        data = np.array(data, dtype=float)
    elif not isinstance(data, np.ndarray):
        data = np.array(data, dtype=float)

    # Handle empty or single-point data
    if len(data) == 0:
        return np.array([]), np.array([], dtype=bool)
    elif len(data) == 1:
        return np.array([0.0]), np.array([False])

    # Default configuration
    if config is None:
        config = {}

    # Initialize modules with configuration
    sampler_config = config.get("sampler", {"method": "identity"})
    denoiser_config = config.get("denoiser", {"method": "identity"})
    detector_config = config.get("detector", {"method": "zscore"})
    filter_config = config.get("filter", {"method": "identity"})

    sampler = SamplerModule(**sampler_config)
    denoiser = DenoiserModule(**denoiser_config)
    detector = DetectorModule(**detector_config)
    filter_module = FilterModule(**filter_config)

    # Process data through pipeline
    # Step 1: Sampling
    sampled_data = sampler.process(data)

    # Step 2: Denoising
    denoised_data = denoiser.process(sampled_data)

    # Step 3: Detection
    scores, flags = detector.process(denoised_data)

    # Step 4: Filtering
    filtered_scores, filtered_flags = filter_module.process(denoised_data, scores, flags)

    # If data was sampled, we need to map results back to original length
    if len(sampled_data) != len(data):
        original_scores = np.zeros(len(data))
        original_flags = np.zeros(len(data), dtype=bool)

        # Simple interpolation for scores and flags
        if len(sampled_data) > 1:
            sample_indices = np.linspace(0, len(data) - 1, len(sampled_data), dtype=int)
            for i, (score, flag) in enumerate(zip(filtered_scores, filtered_flags)):
                original_scores[sample_indices[i]] = score
                original_flags[sample_indices[i]] = flag

        return original_scores, original_flags

    return filtered_scores, filtered_flags
